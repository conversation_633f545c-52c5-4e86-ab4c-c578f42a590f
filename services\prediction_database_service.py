#!/usr/bin/env python3
"""
Prediction Database Service
Handles saving and retrieving predictions from the database for frontend access.
"""

import sys
import os
import json
import logging
from datetime import datetime as dt, date
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import SessionLocal, engine
from database_schema_enhanced import (
    CachedPrediction, 
    CachedFixture, 
    AccumulatorCache, 
    PredictionBatch,
    Base
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PredictionDatabaseService:
    """Service for managing predictions in the database."""
    
    def __init__(self):
        """Initialize the database service."""
        # Ensure tables exist
        Base.metadata.create_all(bind=engine)
    
    def get_db_session(self) -> Session:
        """Get a database session."""
        return SessionLocal()
    
    def save_predictions_to_database(self, predictions_data: List[Dict], diverse_accumulators: Dict[str, List[Dict]], 
                                   prediction_date: str = None) -> bool:
        """Save predictions and accumulators to database."""
        if prediction_date is None:
            prediction_date = dt.now().strftime('%Y-%m-%d')
        
        db = self.get_db_session()
        try:
            # Parse prediction date
            pred_date = dt.strptime(prediction_date, '%Y-%m-%d')
            
            # Create prediction batch record
            batch = PredictionBatch(
                batch_date=pred_date,
                total_fixtures=len(set(pred['fixture_id'] for pred in predictions_data)),
                successful_predictions=len(predictions_data),
                failed_predictions=0,
                started_at=dt.now(),
                completed_at=dt.now(),
                duration_seconds=0.0,  # Will be updated if timing info available
                status='completed'
            )
            
            # Check if batch already exists for this date
            existing_batch = db.query(PredictionBatch).filter(
                PredictionBatch.batch_date == pred_date
            ).first()
            
            if existing_batch:
                # Update existing batch
                existing_batch.successful_predictions = len(predictions_data)
                existing_batch.completed_at = dt.now()
                existing_batch.status = 'completed'
                logger.info(f"📊 Updated existing prediction batch for {prediction_date}")
            else:
                # Create new batch
                db.add(batch)
                logger.info(f"📊 Created new prediction batch for {prediction_date}")
            
            # Delete existing predictions for this date to avoid duplicates
            db.query(CachedPrediction).filter(
                CachedPrediction.prediction_date == pred_date
            ).delete()
            
            # Save individual predictions
            saved_count = 0
            for pred_data in predictions_data:
                try:
                    # Ensure fixture exists
                    fixture = db.query(CachedFixture).filter(
                        CachedFixture.fixture_id == pred_data['fixture_id']
                    ).first()
                    
                    if not fixture:
                        # Create fixture if it doesn't exist
                        fixture = CachedFixture(
                            fixture_id=pred_data['fixture_id'],
                            home_team=pred_data['home_team'],
                            away_team=pred_data['away_team'],
                            league_name=pred_data['league'],
                            fixture_date=dt.strptime(pred_data['match_date'], '%Y-%m-%d'),
                            status='upcoming',
                            api_source='apifootball'
                        )
                        db.add(fixture)
                        db.flush()  # Get the fixture ID
                    
                    # Create prediction record
                    prediction = CachedPrediction(
                        fixture_id=pred_data['fixture_id'],
                        prediction_date=pred_date,
                        prediction_type=pred_data['prediction_type'],
                        prediction_value=pred_data['prediction'],
                        confidence=float(pred_data['confidence']),
                        model_type='ensemble',  # Since we use multiple models
                        model_name='unified_pipeline_v1',
                        feature_count=62,  # Standard feature count
                        feature_version='enhanced_v1',
                        created_at=dt.now()
                    )
                    
                    db.add(prediction)
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Error saving prediction for fixture {pred_data.get('fixture_id', 'unknown')}: {e}")
                    continue
            
            # Save diverse accumulators
            self._save_accumulators_to_database(db, diverse_accumulators, pred_date)
            
            # Commit all changes
            db.commit()
            
            logger.info(f"✅ Successfully saved {saved_count} predictions to database for {prediction_date}")
            logger.info(f"🎰 Saved {len(diverse_accumulators)} accumulator strategies")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error saving predictions to database: {e}")
            db.rollback()
            return False
        finally:
            db.close()
    
    def _save_accumulators_to_database(self, db: Session, diverse_accumulators: Dict[str, List[Dict]], 
                                     prediction_date: dt):
        """Save diverse accumulators to database."""
        # Delete existing accumulators for this date
        db.query(AccumulatorCache).filter(
            AccumulatorCache.prediction_date == prediction_date
        ).delete()
        
        for acc_type, predictions in diverse_accumulators.items():
            if not predictions:
                continue
            
            try:
                # Calculate accumulator statistics
                total_confidence = sum(float(pred.get('confidence', 0)) for pred in predictions)
                avg_confidence = total_confidence / len(predictions) if predictions else 0

                # Estimate total odds (rough calculation)
                estimated_odds = 1.0
                for pred in predictions:
                    conf = float(pred.get('confidence', 75)) / 100
                    individual_odds = 1 / conf if conf > 0 else 1.5
                    estimated_odds *= max(individual_odds, 1.1)

                # Convert numpy types to Python types for JSON serialization
                def convert_numpy_types(obj):
                    """Convert numpy types to Python types for JSON serialization."""
                    if hasattr(obj, 'item'):  # numpy scalar
                        return obj.item()
                    elif isinstance(obj, dict):
                        return {k: convert_numpy_types(v) for k, v in obj.items()}
                    elif isinstance(obj, list):
                        return [convert_numpy_types(item) for item in obj]
                    else:
                        return obj

                # Clean predictions data for JSON serialization
                clean_predictions = convert_numpy_types(predictions)
                
                # Determine risk level based on confidence
                if avg_confidence >= 90:
                    risk_level = 'very_low'
                elif avg_confidence >= 80:
                    risk_level = 'low'
                elif avg_confidence >= 70:
                    risk_level = 'medium'
                else:
                    risk_level = 'high'
                
                # Create accumulator record
                accumulator = AccumulatorCache(
                    prediction_date=prediction_date,
                    accumulator_type=acc_type,
                    is_selected=True,  # All generated accumulators are selected
                    total_odds=round(estimated_odds, 2),
                    game_count=len(predictions),
                    average_confidence=round(avg_confidence, 1),
                    diversity_score=self._calculate_diversity_score(predictions),
                    risk_level=risk_level,
                    recommended_stake=10.0,  # Default stake
                    games_data=json.dumps(clean_predictions),
                    selection_reason=f"Auto-generated {acc_type} accumulator with {len(predictions)} selections",
                    actual_result='pending',
                    created_at=dt.now()
                )
                
                db.add(accumulator)
                
            except Exception as e:
                logger.error(f"❌ Error saving accumulator {acc_type}: {e}")
                continue
    
    def _calculate_diversity_score(self, predictions: List[Dict]) -> float:
        """Calculate diversity score based on prediction types."""
        if not predictions:
            return 0.0
        
        # Count unique prediction types
        pred_types = set(pred.get('prediction_type', '') for pred in predictions)
        
        # Diversity score: percentage of unique types vs total predictions
        diversity = len(pred_types) / len(predictions)
        return round(diversity * 100, 1)
    
    def get_todays_predictions(self, prediction_date: str = None) -> Dict[str, Any]:
        """Get today's predictions from database for frontend."""
        if prediction_date is None:
            prediction_date = dt.now().strftime('%Y-%m-%d')
        
        db = self.get_db_session()
        try:
            pred_date = dt.strptime(prediction_date, '%Y-%m-%d')
            
            # Get predictions with fixture details
            predictions_query = db.query(CachedPrediction, CachedFixture).join(
                CachedFixture, CachedPrediction.fixture_id == CachedFixture.fixture_id
            ).filter(
                CachedPrediction.prediction_date == pred_date
            ).order_by(desc(CachedPrediction.confidence))
            
            predictions_data = []
            for pred, fixture in predictions_query.all():
                pred_dict = {
                    'id': pred.id,
                    'fixture_id': pred.fixture_id,
                    'home_team': fixture.home_team,
                    'away_team': fixture.away_team,
                    'league': fixture.league_name,
                    'match_date': fixture.fixture_date.strftime('%Y-%m-%d'),
                    'match_time': getattr(fixture, 'match_time', ''),
                    'prediction_type': pred.prediction_type,
                    'prediction': pred.prediction_value,
                    'confidence': pred.confidence,
                    'model_type': pred.model_type,
                    'created_at': pred.created_at.isoformat()
                }
                predictions_data.append(pred_dict)
            
            # Get accumulators
            accumulators_query = db.query(AccumulatorCache).filter(
                AccumulatorCache.prediction_date == pred_date,
                AccumulatorCache.is_selected == True
            ).order_by(desc(AccumulatorCache.average_confidence))
            
            accumulators_data = {}
            for acc in accumulators_query.all():
                acc_data = {
                    'id': acc.id,
                    'type': acc.accumulator_type,
                    'total_odds': acc.total_odds,
                    'game_count': acc.game_count,
                    'average_confidence': acc.average_confidence,
                    'diversity_score': acc.diversity_score,
                    'risk_level': acc.risk_level,
                    'recommended_stake': acc.recommended_stake,
                    'games': json.loads(acc.games_data) if acc.games_data else [],
                    'selection_reason': acc.selection_reason,
                    'created_at': acc.created_at.isoformat()
                }
                accumulators_data[acc.accumulator_type] = acc_data
            
            # Get batch info
            batch = db.query(PredictionBatch).filter(
                PredictionBatch.batch_date == pred_date
            ).first()
            
            batch_info = {}
            if batch:
                batch_info = {
                    'total_fixtures': batch.total_fixtures,
                    'successful_predictions': batch.successful_predictions,
                    'status': batch.status,
                    'completed_at': batch.completed_at.isoformat() if batch.completed_at else None
                }
            
            return {
                'status': 'success',
                'date': prediction_date,
                'total_predictions': len(predictions_data),
                'predictions': predictions_data,
                'accumulators': accumulators_data,
                'batch_info': batch_info,
                'retrieved_at': dt.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error retrieving predictions from database: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'date': prediction_date,
                'retrieved_at': dt.now().isoformat()
            }
        finally:
            db.close()
    
    def get_predictions_by_date_range(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Get predictions for a date range."""
        db = self.get_db_session()
        try:
            start_dt = dt.strptime(start_date, '%Y-%m-%d')
            end_dt = dt.strptime(end_date, '%Y-%m-%d')
            
            predictions_query = db.query(CachedPrediction, CachedFixture).join(
                CachedFixture, CachedPrediction.fixture_id == CachedFixture.fixture_id
            ).filter(
                and_(
                    CachedPrediction.prediction_date >= start_dt,
                    CachedPrediction.prediction_date <= end_dt
                )
            ).order_by(desc(CachedPrediction.prediction_date), desc(CachedPrediction.confidence))
            
            predictions_data = []
            for pred, fixture in predictions_query.all():
                pred_dict = {
                    'id': pred.id,
                    'fixture_id': pred.fixture_id,
                    'home_team': fixture.home_team,
                    'away_team': fixture.away_team,
                    'league': fixture.league_name,
                    'match_date': fixture.fixture_date.strftime('%Y-%m-%d'),
                    'prediction_date': pred.prediction_date.strftime('%Y-%m-%d'),
                    'prediction_type': pred.prediction_type,
                    'prediction': pred.prediction_value,
                    'confidence': pred.confidence,
                    'created_at': pred.created_at.isoformat()
                }
                predictions_data.append(pred_dict)
            
            return {
                'status': 'success',
                'start_date': start_date,
                'end_date': end_date,
                'total_predictions': len(predictions_data),
                'predictions': predictions_data,
                'retrieved_at': dt.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error retrieving predictions by date range: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'retrieved_at': dt.now().isoformat()
            }
        finally:
            db.close()

def main():
    """Test the prediction database service."""
    service = PredictionDatabaseService()
    
    # Test retrieving today's predictions
    result = service.get_todays_predictions()
    print("📊 Today's Predictions from Database:")
    print(f"Status: {result['status']}")
    print(f"Total predictions: {result.get('total_predictions', 0)}")
    print(f"Accumulators: {len(result.get('accumulators', {}))}")

if __name__ == "__main__":
    main()
