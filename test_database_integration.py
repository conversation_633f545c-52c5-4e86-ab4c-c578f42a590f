#!/usr/bin/env python3
"""
Test Database Integration
Test the unified pipeline's database integration for frontend access.
"""

import sys
import os
import json
from datetime import datetime as dt

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.prediction_database_service import PredictionDatabaseService

def test_database_service():
    """Test the prediction database service."""
    print("🧪 TESTING DATABASE INTEGRATION")
    print("=" * 60)
    
    # Initialize service
    try:
        db_service = PredictionDatabaseService()
        print("✅ Database service initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize database service: {e}")
        return False
    
    # Test retrieving today's predictions
    print("\n📊 Testing retrieval of today's predictions...")
    try:
        result = db_service.get_todays_predictions()
        print(f"✅ Status: {result['status']}")
        print(f"📅 Date: {result.get('date', 'Unknown')}")
        print(f"🎯 Total predictions: {result.get('total_predictions', 0)}")
        print(f"🎰 Accumulators: {len(result.get('accumulators', {}))}")
        
        if result.get('accumulators'):
            print("\n🎰 Available accumulator strategies:")
            for acc_type, acc_data in result['accumulators'].items():
                print(f"   {acc_type.title()}: {acc_data['game_count']} games, "
                      f"{acc_data['average_confidence']:.1f}% confidence, "
                      f"{acc_data['total_odds']:.2f} odds")
        
        return True
        
    except Exception as e:
        print(f"❌ Error retrieving predictions: {e}")
        return False

def test_json_file_predictions():
    """Test if we have predictions in JSON files."""
    print("\n📁 Testing JSON file predictions...")
    
    json_files = [
        "cache/predictions/latest_predictions.json",
        f"cache/predictions/predictions_{dt.now().strftime('%Y-%m-%d')}.json"
    ]
    
    for json_file in json_files:
        if os.path.exists(json_file):
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                print(f"✅ Found {json_file}")
                print(f"   📅 Date: {data.get('date', 'Unknown')}")
                print(f"   🎯 Predictions: {data.get('total_predictions', 0)}")
                print(f"   🎰 Accumulators: {len(data.get('diverse_accumulators', {}))}")
                print(f"   🔧 Bias fixes: {data.get('bias_fixes_applied', False)}")
                
                return data
                
            except Exception as e:
                print(f"❌ Error reading {json_file}: {e}")
        else:
            print(f"⚠️ File not found: {json_file}")
    
    return None

def simulate_database_save():
    """Simulate saving predictions to database using JSON file data."""
    print("\n💾 Testing database save simulation...")
    
    # Get data from JSON file
    json_data = test_json_file_predictions()
    if not json_data:
        print("❌ No JSON data available for testing")
        return False
    
    try:
        db_service = PredictionDatabaseService()
        
        predictions_data = json_data.get('predictions', [])
        diverse_accumulators = json_data.get('diverse_accumulators', {})
        date_str = json_data.get('date', dt.now().strftime('%Y-%m-%d'))
        
        if not predictions_data:
            print("⚠️ No predictions data to save")
            return False
        
        print(f"📊 Attempting to save {len(predictions_data)} predictions...")
        print(f"🎰 Attempting to save {len(diverse_accumulators)} accumulators...")
        
        success = db_service.save_predictions_to_database(
            predictions_data, diverse_accumulators, date_str
        )
        
        if success:
            print("✅ Successfully saved predictions to database")
            
            # Test retrieval
            print("\n🔍 Testing retrieval after save...")
            result = db_service.get_todays_predictions(date_str)
            print(f"✅ Retrieved {result.get('total_predictions', 0)} predictions")
            print(f"🎰 Retrieved {len(result.get('accumulators', {}))} accumulators")
            
            return True
        else:
            print("❌ Failed to save predictions to database")
            return False
            
    except Exception as e:
        print(f"❌ Error in database save simulation: {e}")
        return False

def test_api_endpoints():
    """Test if we can access predictions via API-like calls."""
    print("\n🌐 Testing API-like access...")
    
    try:
        db_service = PredictionDatabaseService()
        
        # Test today's predictions
        today_result = db_service.get_todays_predictions()
        print(f"✅ Today's predictions API: {today_result['status']}")
        
        # Test date range
        from datetime import timedelta
        start_date = (dt.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        end_date = dt.now().strftime('%Y-%m-%d')
        
        range_result = db_service.get_predictions_by_date_range(start_date, end_date)
        print(f"✅ Date range API: {range_result['status']}")
        print(f"   📊 Total predictions in range: {range_result.get('total_predictions', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing API endpoints: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 BETSIGHTLY DATABASE INTEGRATION TEST")
    print("=" * 80)
    print("🎯 Testing unified pipeline database integration for frontend access")
    print("📅 Test Date:", dt.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 80)
    
    tests = [
        ("Database Service Initialization", test_database_service),
        ("JSON File Predictions", lambda: test_json_file_predictions() is not None),
        ("Database Save Simulation", simulate_database_save),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"   ❌ FAILED: {e}")
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 60)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Database integration is working.")
        print("\n📋 NEXT STEPS FOR FRONTEND:")
        print("1. Run the unified pipeline: python run_unified_pipeline.py")
        print("2. Start the API server: python api/predictions_api.py")
        print("3. Access predictions at: http://localhost:8000/predictions/today")
        print("4. View API docs at: http://localhost:8000/docs")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Ensure database is initialized: python initialize_enhanced_database.py")
        print("2. Run the unified pipeline first: python run_unified_pipeline.py")
        print("3. Check database connection and permissions")

if __name__ == "__main__":
    main()
