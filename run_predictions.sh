#!/bin/bash

echo "🚀 BETSIGHTLY PREDICTIONS RUNNER"
echo "================================"
echo ""

# Check if we're in the right directory
if [ ! -f "run_unified_pipeline.py" ]; then
    echo "❌ Error: Please run this from the betsightly-backend directory"
    echo "📁 Navigate to: cd /home/<USER>/Desktop/betsightly-backend"
    exit 1
fi

echo "📁 Current directory: $(pwd)"
echo ""

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

if [ $? -eq 0 ]; then
    echo "✅ Virtual environment activated"
else
    echo "❌ Failed to activate virtual environment"
    echo "💡 Try: python -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

echo ""
echo "🎯 RUNNING UNIFIED PREDICTION PIPELINE..."
echo "========================================="

# Run the unified pipeline
python run_unified_pipeline.py

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Unified pipeline completed successfully!"
    echo ""
    
    # Generate text file output
    echo "📝 GENERATING TEXT FILE OUTPUT..."
    echo "================================"
    python generate_predictions_with_txt.py
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 ALL DONE! Your predictions are ready:"
        echo ""
        echo "📊 JSON Files (for API/database):"
        echo "   - cache/predictions/latest_predictions.json"
        echo "   - cache/predictions/predictions_$(date +%Y-%m-%d).json"
        echo ""
        echo "📝 Text Files (human readable):"
        echo "   - predictions_output/latest_predictions.txt"
        echo "   - predictions_output/predictions_$(date +%Y-%m-%d).txt"
        echo ""
        echo "🌐 To start the API server for frontend access:"
        echo "   python api/predictions_api.py"
        echo ""
        echo "📖 View your predictions:"
        echo "   cat predictions_output/latest_predictions.txt"
        echo ""
    else
        echo "⚠️ Text file generation had issues, but JSON files are available"
    fi
    
else
    echo "❌ Pipeline failed. Check the error messages above."
    exit 1
fi
