#!/usr/bin/env python3
"""
Analyze Accumulator Odds Categories
Analyze why only 2.0 odds category was built and others failed.
"""

import json
import os
from datetime import datetime as dt

def estimate_prediction_odds(confidence, prediction_type):
    """Estimate odds for a prediction based on confidence and type (same logic as pipeline)."""
    # Base odds estimation from confidence
    confidence_odds = 1.0 / (confidence / 100.0)
    
    # Adjust based on prediction type (updated to match pipeline)
    type_multipliers = {
        'match_result': 1.2,
        'over_2_5': 1.0,
        'btts': 1.05,
        'clean_sheet_home': 1.15,
        'clean_sheet_away': 1.2
    }
    
    multiplier = type_multipliers.get(prediction_type, 1.0)
    estimated_odds = confidence_odds * multiplier
    
    # Ensure reasonable bounds (updated to match pipeline)
    return max(1.1, min(2.5, estimated_odds))

def analyze_odds_categories():
    """Analyze today's predictions and odds categorization."""
    today_str = dt.now().strftime('%Y-%m-%d')
    json_file = f"cache/predictions/predictions_{today_str}.json"
    
    if not os.path.exists(json_file):
        print(f"❌ No predictions found for {today_str}")
        return
    
    # Load predictions
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    predictions = data.get('predictions', [])
    
    print("🔍 ACCUMULATOR ODDS ANALYSIS")
    print("=" * 80)
    print(f"📅 Date: {data['date']}")
    print(f"🎯 Total Predictions: {len(predictions)}")
    print("=" * 80)
    
    # Define accumulator categories (same as pipeline)
    accumulator_categories = {
        '2.0': {
            'target_odds': 2.0,
            'max_odds': 2.2,
            'min_confidence': 80.0,
            'allowed_risk': ['very_low'],
            'individual_odds_range': (1.1, 1.3),
            'typical_selections': (2, 3)
        },
        '5.0': {
            'target_odds': 5.0,
            'max_odds': 5.5,
            'min_confidence': 70.0,
            'allowed_risk': ['very_low', 'low'],
            'individual_odds_range': (1.2, 1.6),
            'typical_selections': (3, 5)
        },
        '10.0': {
            'target_odds': 10.0,
            'max_odds': 11.0,
            'min_confidence': 65.0,
            'allowed_risk': ['very_low', 'low'],
            'individual_odds_range': (1.4, 2.0),
            'typical_selections': (4, 6)
        },
        '20.0': {
            'target_odds': 20.0,
            'max_odds': 22.0,
            'min_confidence': 60.0,
            'allowed_risk': ['very_low', 'low'],
            'individual_odds_range': (1.6, 2.2),
            'typical_selections': (5, 8)
        }
    }
    
    # Analyze each prediction
    print("\n📊 INDIVIDUAL PREDICTION ANALYSIS")
    print("-" * 80)
    
    for i, pred in enumerate(predictions, 1):
        confidence = pred['confidence']
        risk_level = pred['risk_level']
        prediction_type = pred['prediction_type']
        
        estimated_odds = estimate_prediction_odds(confidence, prediction_type)
        
        print(f"\n{i}. {pred['home_team']} vs {pred['away_team']}")
        print(f"   🎯 Type: {prediction_type}")
        print(f"   📈 Confidence: {confidence}%")
        print(f"   🛡️  Risk: {risk_level}")
        print(f"   💰 Estimated Odds: {estimated_odds:.2f}")
        
        # Check which categories this prediction qualifies for
        eligible_categories = []
        for cat_name, cat_config in accumulator_categories.items():
            # Check confidence
            if confidence < cat_config['min_confidence']:
                continue
            
            # Check risk level
            if risk_level not in cat_config['allowed_risk']:
                continue
            
            # Check odds range
            min_odds, max_odds = cat_config['individual_odds_range']
            if min_odds <= estimated_odds <= max_odds:
                eligible_categories.append(cat_name)
        
        if eligible_categories:
            print(f"   ✅ Eligible for: {', '.join(eligible_categories)} odds categories")
        else:
            print(f"   ❌ Not eligible for any odds category")
    
    # Analyze category eligibility
    print("\n\n🎰 ODDS CATEGORY ANALYSIS")
    print("=" * 80)
    
    for cat_name, cat_config in accumulator_categories.items():
        print(f"\n🎯 {cat_name} ODDS CATEGORY")
        print(f"   Target: {cat_config['target_odds']} odds")
        print(f"   Max: {cat_config['max_odds']} odds")
        print(f"   Min Confidence: {cat_config['min_confidence']}%")
        print(f"   Allowed Risk: {cat_config['allowed_risk']}")
        print(f"   Individual Odds Range: {cat_config['individual_odds_range']}")
        print(f"   Required Selections: {cat_config['typical_selections']}")
        
        # Count eligible predictions
        eligible_count = 0
        eligible_predictions = []
        
        for pred in predictions:
            confidence = pred['confidence']
            risk_level = pred['risk_level']
            prediction_type = pred['prediction_type']
            estimated_odds = estimate_prediction_odds(confidence, prediction_type)
            
            # Check all criteria
            if (confidence >= cat_config['min_confidence'] and
                risk_level in cat_config['allowed_risk'] and
                cat_config['individual_odds_range'][0] <= estimated_odds <= cat_config['individual_odds_range'][1]):
                eligible_count += 1
                eligible_predictions.append((pred, estimated_odds))
        
        print(f"   📊 Eligible Predictions: {eligible_count}")
        min_selections, max_selections = cat_config['typical_selections']
        print(f"   📋 Required: {min_selections}-{max_selections} selections")
        
        if eligible_count >= min_selections:
            print(f"   ✅ CAN BUILD: Enough predictions available")
            
            # Simulate accumulator building
            eligible_predictions.sort(key=lambda x: x[1])  # Sort by odds
            selected = eligible_predictions[:max_selections]
            combined_odds = 1.0
            for _, odds in selected:
                combined_odds *= odds
            
            print(f"   💰 Simulated Combined Odds: {combined_odds:.2f}")
            if combined_odds <= cat_config['max_odds']:
                print(f"   ✅ Within max odds limit ({cat_config['max_odds']})")
            else:
                print(f"   ❌ Exceeds max odds limit ({cat_config['max_odds']})")
        else:
            print(f"   ❌ CANNOT BUILD: Need {min_selections-eligible_count} more predictions")
    
    print("\n\n🔧 RECOMMENDATIONS")
    print("=" * 80)
    print("1. Most predictions have very high confidence (75-93%) leading to low odds (1.1-1.3)")
    print("2. Higher odds categories need predictions with lower confidence or different types")
    print("3. Consider adjusting individual_odds_range for higher categories")
    print("4. May need to include 'medium' risk level predictions for higher categories")
    print("5. Current filtering is very conservative, limiting higher odds opportunities")

if __name__ == "__main__":
    analyze_odds_categories()
