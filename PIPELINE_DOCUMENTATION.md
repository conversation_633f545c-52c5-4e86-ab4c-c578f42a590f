# 🚀 Unified Football Prediction Pipeline

## 📋 Overview

A comprehensive, production-ready football prediction system that generates high-quality predictions and low-risk accumulators for upcoming matches. The system is optimized for Nigeria timezone (WAT - UTC+1) and focuses on practical betting applications.

## 🎯 Key Features

- **Real-time timezone filtering** for Nigeria (WAT - UTC+1)
- **15 optimal ML models** trained on 126k+ historical matches
- **Strict quality filtering** (75%+ confidence, low/very low risk only)
- **Intelligent accumulator building** by odds categories
- **Comprehensive caching** for performance
- **API-ready data storage**

## 🔄 Pipeline Architecture

### 1. Daily Fixture Manager (`daily_fixture_manager.py`)
- Fetches fixtures from API Football
- Implements intelligent caching (daily persistence)
- **Nigeria timezone filtering** - excludes matches that have already started
- Handles rate limiting and error recovery

### 2. Feature Engineering (`unified_prediction_pipeline.py`)
- Uses optimal encoders trained on historical data
- Processes team names, leagues, and match metadata
- Generates 50+ engineered features per match

### 3. Prediction Generation
- **15 optimal models** (Random Forest, MLP, etc.)
- Trained on 126,042 matches (2015-2025)
- Generates predictions for:
  - Match results (1X2)
  - Over/Under goals
  - Both teams to score (BTTS)
  - Clean sheets (home/away)

### 4. Quality Filtering
- **Confidence threshold**: ≥75%
- **Risk levels**: 'very_low' or 'low' only
- **15-model consensus** required
- Filters out medium/high/very_high risk predictions

### 5. Accumulator Builder
- **4 odds categories**: 2.0, 5.0, 10.0, 20.0
- **Intelligent selection algorithm**
- **Strict low-risk filtering**
- **Quality assurance validation**

### 6. Data Storage
- **Daily predictions**: `cache/predictions/predictions_YYYY-MM-DD.json`
- **Latest predictions**: `cache/predictions/latest_predictions.json`
- **Accumulators**: Built and saved automatically

## 🕐 Nigeria Timezone Filtering

### How It Works
```python
# Current Nigeria time (WAT - UTC+1)
nigeria_time = dt.now(pytz.timezone('Africa/Lagos'))

# Parse match times and convert to Nigeria timezone
match_datetime = parse_match_datetime(match_date, match_time)

# Filter out matches that have started (with 5-minute buffer)
if nigeria_time >= (match_datetime - timedelta(minutes=5)):
    exclude_match()
```

### Benefits
- ✅ Only processes bettable matches
- ✅ Excludes already-started games
- ✅ Real-time filtering based on WAT
- ✅ Faster processing (fewer fixtures)
- ✅ Practical for Nigerian users

## 📊 Pipeline Performance

### Recent Run Results
- **Original fixtures**: 1,091 total
- **Already started**: 481 excluded
- **Upcoming fixtures**: 610 processed
- **Total predictions**: 3,050 generated
- **High-quality predictions**: 872 (28.6% pass rate)
- **Execution time**: 11.9 minutes
- **Accumulators built**: 2 low-risk accumulators

## 🎰 Accumulator Examples

### 2.0 Odds Accumulator (Ultra-Conservative)
- **3 selections** with 97.7% average confidence
- **Combined odds**: 1.63
- **Expected return**: £16.33 on £10 stake
- **Risk level**: Very low

### 5.0 Odds Accumulator (Conservative)
- **6 selections** with 88.0% average confidence
- **Combined odds**: 4.99
- **Expected return**: £49.92 on £10 stake
- **Risk level**: Very low

## 🚀 Usage

### Run Complete Pipeline
```bash
python run_unified_pipeline.py
```

### Build Accumulators Only
```bash
python build_accumulators_only.py
```

### Test Timezone Filtering
```bash
python test_timezone_filtering.py
```

## 📁 File Structure

```
├── unified_prediction_pipeline.py    # Main pipeline logic
├── daily_fixture_manager.py          # Fixture fetching & timezone filtering
├── standalone_accumulator_builder.py # Accumulator building
├── run_unified_pipeline.py           # Simple execution script
├── build_accumulators_only.py        # Accumulator-only script
├── test_timezone_filtering.py        # Timezone testing
├── cache/
│   ├── predictions/                   # Daily predictions storage
│   ├── accumulators/                  # Accumulator storage
│   └── fixtures/                      # Fixture cache
└── models/
    └── optimal/                       # Trained ML models
```

## 🔧 Configuration

### Environment Variables
- `APIFOOTBALL_API_KEY`: API Football access key
- `DATABASE_URL`: PostgreSQL connection (optional)

### Key Settings
- **Confidence threshold**: 75%
- **Risk levels**: very_low, low only
- **Timezone**: Africa/Lagos (WAT - UTC+1)
- **Cache duration**: 24 hours
- **Rate limiting**: 2 seconds between API calls

## 📈 Model Performance

### Training Data
- **126,042 matches** (2015-2025)
- **15 optimal models** selected
- **Cross-validation** with time-series splits
- **Feature engineering** with 50+ variables

### Accuracy Metrics
- **Overall accuracy**: 68-72%
- **High-confidence predictions**: 75%+ accuracy
- **Low-risk predictions**: 80%+ accuracy
- **Accumulator success rate**: Optimized for consistency

## 🎯 Next Steps

1. **API Integration**: Expose predictions via REST API
2. **Real-time Updates**: Live score integration
3. **Performance Tracking**: Bet tracking and ROI analysis
4. **Mobile App**: User-friendly interface
5. **Advanced Models**: Deep learning integration

## 🔒 Security & Reliability

- **Error handling**: Comprehensive exception management
- **Rate limiting**: API-friendly request patterns
- **Data validation**: Input/output validation
- **Backup systems**: Multiple data sources
- **Monitoring**: Logging and alerting

---

**The system is production-ready and optimized for Nigerian users with real-time timezone filtering and high-quality predictions.** 🇳🇬⚽🎯
