#!/usr/bin/env python3
"""
Extract Odds-Based Accumulators
Creates a readable text file with the 4 odds-based accumulator categories.
"""

import json
import os
from datetime import datetime as dt

def extract_odds_accumulators():
    """Extract and display odds-based accumulators."""
    
    # Load the latest predictions
    try:
        with open('cache/predictions/latest_predictions.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ No predictions file found. Run the pipeline first!")
        return
    
    # Create output directory
    os.makedirs('predictions_output', exist_ok=True)
    
    # Create the odds-based accumulators text file
    filename = 'predictions_output/odds_based_accumulators.txt'
    
    with open(filename, 'w') as f:
        f.write('🎯 BETSIGHTLY ODDS-BASED ACCUMULATORS\n')
        f.write('=' * 80 + '\n')
        f.write(f'📅 Date: {data.get("date", "2025-08-10")}\n')
        f.write(f'🕐 Generated: {dt.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
        f.write(f'🎯 Total Predictions: {data.get("total_predictions", 0)}\n')
        f.write('=' * 80 + '\n\n')
        
        # Get diverse accumulators
        accumulators = data.get('diverse_accumulators', {})
        
        if not accumulators:
            f.write('❌ No accumulators found in the data.\n')
            return
        
        f.write('🎰 AVAILABLE ACCUMULATOR STRATEGIES\n')
        f.write('-' * 80 + '\n\n')
        
        for acc_type, games in accumulators.items():
            if not games:
                continue
                
            f.write(f'🎯 {acc_type.upper().replace("_", " ")} ACCUMULATOR\n')
            f.write('-' * 50 + '\n')
            f.write(f'📊 Number of Games: {len(games)}\n')
            
            # Calculate average confidence
            confidences = []
            for game in games:
                conf_str = str(game.get('confidence', '0')).replace('%', '')
                try:
                    conf_val = float(conf_str)
                    confidences.append(conf_val)
                except ValueError:
                    confidences.append(0)
            
            avg_conf = sum(confidences) / len(confidences) if confidences else 0
            f.write(f'📈 Average Confidence: {avg_conf:.1f}%\n')
            
            # Estimate combined odds
            estimated_odds = 1.0
            for conf in confidences:
                conf_decimal = conf / 100 if conf > 0 else 0.75
                individual_odds = 1 / conf_decimal if conf_decimal > 0 else 1.5
                estimated_odds *= max(individual_odds, 1.1)
            
            f.write(f'💰 Estimated Combined Odds: {estimated_odds:.2f}\n')
            f.write(f'💵 Recommended Stake: £10\n')
            f.write(f'💸 Potential Return: £{estimated_odds * 10:.2f}\n\n')
            
            f.write('🎮 GAMES IN THIS ACCUMULATOR:\n')
            for i, game in enumerate(games, 1):
                f.write(f'{i}. {game.get("home_team", "Unknown")} vs {game.get("away_team", "Unknown")}\n')
                f.write(f'   🎯 Prediction: {game.get("prediction_type", "Unknown")} = {game.get("prediction", "Unknown")}\n')
                f.write(f'   📈 Confidence: {game.get("confidence", "Unknown")}%\n')
                f.write(f'   🏆 League: {game.get("league", "Unknown")}\n')
                f.write(f'   📅 Date: {game.get("match_date", data.get("date", "2025-08-10"))}\n')
                f.write(f'   🆔 Fixture ID: {game.get("fixture_id", "Unknown")}\n')
                f.write('\n')
            
            f.write('\n' + '=' * 50 + '\n\n')
        
        # Add analysis of odds categories
        predictions = data.get('predictions', [])
        odds_breakdown = {}
        
        for pred in predictions:
            odds_cat = pred.get('odds_category', 'unknown')
            if odds_cat not in odds_breakdown:
                odds_breakdown[odds_cat] = 0
            odds_breakdown[odds_cat] += 1
        
        f.write('📋 ODDS CATEGORY BREAKDOWN\n')
        f.write('-' * 80 + '\n')
        f.write('Based on today\'s analysis:\n\n')
        
        for odds_cat in ['2.0', '5.0', '10.0', '20.0']:
            count = odds_breakdown.get(odds_cat, 0)
            if count > 0:
                f.write(f'✅ {odds_cat} Odds Category: {count} predictions available\n')
            else:
                f.write(f'❌ {odds_cat} Odds Category: No predictions available\n')
        
        f.write('\n🔍 EXPLANATION:\n')
        f.write('The system prioritizes high-confidence, low-risk predictions.\n')
        f.write('Higher odds categories (5.0, 10.0, 20.0) require riskier predictions\n')
        f.write('which don\'t meet our strict 75%+ confidence threshold.\n\n')
        
        f.write('🎯 RECOMMENDATION:\n')
        f.write('Focus on the Conservative and Balanced accumulators shown above\n')
        f.write('for the best risk-adjusted returns.\n\n')
        
        f.write('📊 SUMMARY:\n')
        f.write(f'• Total Accumulators Generated: {len(accumulators)}\n')
        f.write(f'• Total Individual Predictions: {len(predictions)}\n')
        f.write(f'• Bias Fixes Applied: {data.get("bias_fixes_applied", False)}\n')
        f.write(f'• Generated: {data.get("generation_time", "Unknown")}\n')
    
    print(f'✅ Odds-based accumulators saved to: {filename}')
    
    # Display summary
    print('\n🎯 SUMMARY OF AVAILABLE ACCUMULATORS:')
    print('=' * 60)
    
    for acc_type, games in accumulators.items():
        if games:
            confidences = []
            for game in games:
                conf_str = str(game.get('confidence', '0')).replace('%', '')
                try:
                    conf_val = float(conf_str)
                    confidences.append(conf_val)
                except ValueError:
                    confidences.append(0)
            
            avg_conf = sum(confidences) / len(confidences) if confidences else 0
            print(f'✅ {acc_type.upper()}: {len(games)} games, {avg_conf:.1f}% avg confidence')
    
    print(f'\n📁 Full details saved to: {filename}')
    return filename

if __name__ == "__main__":
    extract_odds_accumulators()
