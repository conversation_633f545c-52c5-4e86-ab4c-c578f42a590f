#!/usr/bin/env python3
"""
Create a clean repository with only essential files for GitHub push
"""

import os
import shutil
import subprocess
from pathlib import Path

def create_clean_repo():
    """Create a clean repository with only essential files"""
    
    # Essential files to include
    essential_files = [
        # Main pipeline files
        'unified_prediction_pipeline.py',
        'optimal_trainer.py',
        'daily_fixture_manager.py',
        'smart_model_manager.py',
        'comprehensive_model_trainer.py',
        
        # Supporting files
        'extensive_model_trainer.py',
        'hybrid_model_trainer.py',
        'model_status_report.py',
        'test_optimal_models.py',
        'show_prediction_results.py',
        'verification_summary.py',
        
        # Configuration files
        'requirements.txt',
        'requirements-production.txt',
        'requirements-render.txt',
        'requirements_enhanced.txt',
        'runtime.txt',
        'Procfile',
        'render.yaml',
        
        # Documentation
        'README.md',
        'UNIFIED_PIPELINE_README.md',
        'COMPLETE_PIPELINE_README.md',
        'COMPREHENSIVE_ML_TEST_RESULTS.md',
        'PERFECT_PREDICTIONS_STRATEGY.md',
        'PIPELINE_SETUP_GUIDE.md',
        'SYSTEM_ARCHITECTURE.md',
        'APIFOOTBALL_INTEGRATION_SUMMARY.md',
        
        # Core application files
        'main.py',
        'database.py',
        'database_schema_enhanced.py',
        'initialize_enhanced_database.py',
        'fixture.py',
        'prediction.py',
        'punter.py',
        'bookmaker.py',
        'betting_code.py',
        'telegram_bot.py',
        'telegram_db.py',
        
        # Git files
        '.gitignore',
    ]
    
    # Essential directories to include (with their contents)
    essential_dirs = [
        'api/',
        'app/',
        'services/',
        'ml/',
        'utils/',
        'schemas/',
        'alembic/',
        'basketball/',
    ]
    
    # Create clean directory
    clean_dir = Path('../betsightly-clean')
    if clean_dir.exists():
        shutil.rmtree(clean_dir)
    clean_dir.mkdir()
    
    print(f"Creating clean repository in {clean_dir}")
    
    # Copy essential files
    for file in essential_files:
        if Path(file).exists():
            shutil.copy2(file, clean_dir / file)
            print(f"Copied: {file}")
    
    # Copy essential directories
    for dir_name in essential_dirs:
        src_dir = Path(dir_name)
        if src_dir.exists():
            dst_dir = clean_dir / dir_name
            shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns('*.pkl', '*.joblib', '*.csv', '__pycache__'))
            print(f"Copied directory: {dir_name}")
    
    # Create empty directories for runtime
    runtime_dirs = [
        'models/',
        'data/',
        'cache/',
        'data/fixtures/',
        'data/github/',
        'data/optimal/',
    ]
    
    for dir_name in runtime_dirs:
        (clean_dir / dir_name).mkdir(parents=True, exist_ok=True)
        # Create .gitkeep files
        (clean_dir / dir_name / '.gitkeep').touch()
        print(f"Created empty directory: {dir_name}")
    
    # Initialize git repository
    os.chdir(clean_dir)
    
    # Initialize git
    subprocess.run(['git', 'init'], check=True)
    subprocess.run(['git', 'add', '.'], check=True)
    subprocess.run(['git', 'commit', '-m', 'Initial commit: Clean unified prediction pipeline'], check=True)
    
    print(f"\nClean repository created in {clean_dir.absolute()}")
    print("To push to GitHub:")
    print(f"cd {clean_dir.absolute()}")
    print("git remote add origin https://github.com/mentorzillab/betsightly-backend.git")
    print("git branch -M unified-pipeline-clean")
    print("git push -u origin unified-pipeline-clean")

if __name__ == "__main__":
    create_clean_repo()
