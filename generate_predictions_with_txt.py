#!/usr/bin/env python3
"""
Generate Today's Predictions with Text File Output
Runs the unified pipeline and creates a readable text file copy.
"""

import sys
import os
import json
from datetime import datetime as dt

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unified_prediction_pipeline import UnifiedPredictionPipeline

def create_text_file_output(predictions_data, diverse_accumulators, date_str):
    """Create a readable text file with today's predictions."""
    
    # Create output directory
    output_dir = "predictions_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create filename
    filename = f"predictions_{date_str}.txt"
    filepath = os.path.join(output_dir, filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        # Header
        f.write("🎯 BETSIGHTLY FOOTBALL PREDICTIONS\n")
        f.write("=" * 80 + "\n")
        f.write(f"📅 Date: {date_str}\n")
        f.write(f"🕐 Generated: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"🎯 Total Predictions: {len(predictions_data)}\n")
        f.write(f"🎰 Accumulator Strategies: {len(diverse_accumulators)}\n")
        f.write("=" * 80 + "\n\n")
        
        # Individual Predictions
        f.write("📊 INDIVIDUAL PREDICTIONS\n")
        f.write("-" * 80 + "\n")
        
        # Group by league
        leagues = {}
        for pred in predictions_data:
            league = pred.get('league', 'Unknown League')
            if league not in leagues:
                leagues[league] = []
            leagues[league].append(pred)
        
        for league, league_preds in leagues.items():
            f.write(f"\n🏆 {league.upper()}\n")
            f.write("-" * 40 + "\n")
            
            for pred in league_preds:
                f.write(f"⚽ {pred['home_team']} vs {pred['away_team']}\n")
                f.write(f"   📅 Date: {pred['match_date']} {pred.get('match_time', '')}\n")
                f.write(f"   🎯 Prediction: {pred['prediction_type']} = {pred['prediction']}\n")
                f.write(f"   📈 Confidence: {pred['confidence']:.1f}%\n")
                f.write(f"   ⚠️  Risk Level: {pred.get('risk_level', 'N/A')}\n")
                f.write(f"   🆔 Fixture ID: {pred['fixture_id']}\n")
                f.write("\n")
        
        # Accumulator Strategies
        f.write("\n" + "=" * 80 + "\n")
        f.write("🎰 ACCUMULATOR STRATEGIES\n")
        f.write("=" * 80 + "\n")
        
        for strategy_name, games in diverse_accumulators.items():
            if not games:
                continue
                
            f.write(f"\n🎯 {strategy_name.upper().replace('_', ' ')} STRATEGY\n")
            f.write("-" * 50 + "\n")
            f.write(f"📊 Number of Games: {len(games)}\n")
            
            # Calculate average confidence
            total_conf = sum(float(game.get('confidence', 0)) for game in games)
            avg_conf = total_conf / len(games) if games else 0
            f.write(f"📈 Average Confidence: {avg_conf:.1f}%\n")
            
            # Calculate estimated odds
            estimated_odds = 1.0
            for game in games:
                conf = float(game.get('confidence', 75)) / 100
                individual_odds = 1 / conf if conf > 0 else 1.5
                estimated_odds *= max(individual_odds, 1.1)
            
            f.write(f"💰 Estimated Combined Odds: {estimated_odds:.2f}\n")
            f.write(f"💵 Recommended Stake: £10\n")
            f.write(f"💸 Potential Return: £{estimated_odds * 10:.2f}\n\n")
            
            f.write("🎮 GAMES IN THIS ACCUMULATOR:\n")
            for i, game in enumerate(games, 1):
                f.write(f"{i}. {game['home_team']} vs {game['away_team']}\n")
                f.write(f"   🎯 Prediction: {game['prediction_type']} = {game['prediction']}\n")
                f.write(f"   📈 Confidence: {game['confidence']:.1f}%\n")
                f.write(f"   🏆 League: {game.get('league', 'Unknown')}\n")
                f.write(f"   📅 Date: {game['match_date']}\n")
                f.write("\n")
        
        # Summary
        f.write("\n" + "=" * 80 + "\n")
        f.write("📋 SUMMARY\n")
        f.write("=" * 80 + "\n")
        f.write(f"✅ Total Predictions Generated: {len(predictions_data)}\n")
        f.write(f"🎰 Accumulator Strategies Created: {len(diverse_accumulators)}\n")
        f.write(f"🔍 All predictions meet strict criteria:\n")
        f.write(f"   - Confidence ≥ 75%\n")
        f.write(f"   - Risk level: very_low or low only\n")
        f.write(f"   - Bias fixes applied for balanced predictions\n")
        f.write(f"📅 Generated on: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("\n🎯 Good luck with your bets! 🍀\n")
    
    return filepath

def main():
    """Main function to generate predictions with text output."""
    print("🚀 GENERATING TODAY'S PREDICTIONS WITH TEXT FILE OUTPUT")
    print("=" * 80)
    
    try:
        # Initialize pipeline
        print("🤖 Initializing unified prediction pipeline...")
        pipeline = UnifiedPredictionPipeline()
        
        # Run pipeline
        print("🎯 Running prediction pipeline...")
        results = pipeline.run_daily_predictions()
        
        if results['status'] == 'success':
            print("✅ Pipeline completed successfully!")
            
            # Get today's date
            today_str = dt.now().strftime('%Y-%m-%d')
            
            # Load the saved predictions
            json_file = f"cache/predictions/predictions_{today_str}.json"
            if os.path.exists(json_file):
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                predictions_data = data.get('predictions', [])
                diverse_accumulators = data.get('diverse_accumulators', {})
                
                # Create text file
                print("📝 Creating text file output...")
                txt_filepath = create_text_file_output(predictions_data, diverse_accumulators, today_str)
                
                print(f"✅ Text file created: {txt_filepath}")
                print(f"📊 Total predictions: {len(predictions_data)}")
                print(f"🎰 Accumulator strategies: {len(diverse_accumulators)}")
                
                # Also create a "latest" copy
                latest_filepath = os.path.join("predictions_output", "latest_predictions.txt")
                import shutil
                shutil.copy2(txt_filepath, latest_filepath)
                print(f"📋 Latest copy: {latest_filepath}")
                
            else:
                print(f"❌ JSON file not found: {json_file}")
                
        else:
            print(f"❌ Pipeline failed: {results.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
