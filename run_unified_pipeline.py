#!/usr/bin/env python3
"""
Run Unified Pipeline
Single script to run the complete unified prediction pipeline.
Generates predictions, saves them, and builds low-risk accumulators.
"""

import sys
import os
from datetime import datetime as dt

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unified_prediction_pipeline import UnifiedPredictionPipeline

def main():
    """Run the unified pipeline with comprehensive output."""
    
    print("🚀 UNIFIED FOOTBALL PREDICTION PIPELINE")
    print("="*80)
    print("🎯 Comprehensive Football Prediction System")
    print("📊 Using optimal models trained on 126k+ matches")
    print("🔍 Strict filtering: confidence ≥75%, risk 'very_low'/'low' only")
    print("🎰 Automatic accumulator building by odds categories")
    print("📅 Date:", dt.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("="*80)
    
    try:
        # Initialize pipeline
        pipeline = UnifiedPredictionPipeline()
        pipeline.min_confidence = 75.0  # Strict confidence threshold
        
        print("\n🤖 Initializing pipeline...")
        print("✅ Models loaded")
        print("✅ Encoders ready")
        print("✅ Accumulator builder configured")
        
        # Run the unified pipeline
        print("\n🚀 Running unified pipeline...")
        results = pipeline.run_unified_pipeline()
        
        if results['status'] == 'success':
            print("\n🎉 PIPELINE COMPLETED SUCCESSFULLY!")
            print("="*60)
            
            # Extract results
            pipeline_results = results['pipeline_results']
            accumulators = results['accumulators']
            summary = pipeline_results['pipeline_summary']
            
            # Show comprehensive summary
            print(f"\n📊 EXECUTION SUMMARY:")
            print(f"⏱️  Execution Time: {summary['execution_time_seconds']:.1f} seconds")
            print(f"🏈 Fixtures Processed: {summary['total_fixtures_processed']}")
            print(f"🎯 Total Predictions Generated: {summary['total_predictions_generated']}")
            print(f"✅ High-Quality Predictions: {summary.get('high_quality_predictions', 'N/A')}")
            print(f"📈 Pass Rate: {summary.get('pass_rate', 0):.1f}%")
            print(f"🎰 Accumulators Built: {summary.get('accumulators_built', 'N/A')}")
            
            # Show prediction breakdown
            if 'prediction_breakdown' in pipeline_results:
                breakdown = pipeline_results['prediction_breakdown']
                print(f"\n🎯 PREDICTION BREAKDOWN:")
                for pred_type, count in breakdown.items():
                    print(f"   {pred_type.replace('_', ' ').title()}: {count}")
            
            # Show accumulator summary
            if accumulators:
                print(f"\n🎰 ACCUMULATOR SUMMARY:")
                for acc in accumulators:
                    print(f"   {acc.odds_category} Odds: {acc.num_selections} selections, "
                          f"{acc.estimated_odds:.2f} combined odds, "
                          f"{acc.avg_confidence:.1f}% avg confidence, "
                          f"{acc.risk_level} risk")
            
            # Show file locations
            print(f"\n📁 SAVED FILES:")
            print(f"   Predictions: cache/predictions/predictions_{dt.now().strftime('%Y-%m-%d')}.json")
            print(f"   Latest Predictions: cache/predictions/latest_predictions.json")
            if 'results_file' in results:
                print(f"   Full Results: {results['results_file']}")
            
            print(f"\n🔄 NEXT STEPS:")
            print(f"   • Use standalone_accumulator_builder.py to rebuild accumulators")
            print(f"   • Access saved predictions programmatically")
            print(f"   • Review individual predictions in detail")
            
            print(f"\n✅ ALL TASKS COMPLETED SUCCESSFULLY!")
            return True
            
        else:
            print(f"\n❌ PIPELINE FAILED: {results.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"\n❌ ERROR DURING EXECUTION: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎊 Ready for betting! All predictions and accumulators are available.")
    else:
        print("\n💥 Something went wrong. Check the error messages above.")
