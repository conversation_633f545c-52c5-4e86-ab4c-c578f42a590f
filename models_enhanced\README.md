# 🤖 Enhanced Models Directory

## 📁 **Model Files**

This directory contains the trained machine learning models for the Betsightly prediction system. Some large model files (>50MB) are excluded from git to keep the repository lightweight.

### ✅ **Available Models**
- `lightgbm_*.pkl` - LightGBM models for various predictions
- `xgboost_*.pkl` - XGBoost models for various predictions
- `feature_columns.pkl` - Feature column definitions

### ❌ **Excluded Large Models**
- `random_forest_btts.pkl` - Random Forest BTTS model (>50MB)
- `random_forest_over_2_5.pkl` - Random Forest Over 2.5 goals model (>50MB)

## 🔧 **How to Regenerate Models**

If you need the complete set of models, run the optimal trainer:

```bash
# Activate virtual environment
source venv/bin/activate

# Train all models (this will recreate missing models)
python optimal_trainer.py
```

This will:
1. Load training data from the database
2. Train all model types (LightGBM, XGBoost, Random Forest)
3. Save models to this directory
4. Create the model registry

## 📊 **Model Performance**

The system automatically selects the best performing models for each prediction type. The unified pipeline uses these models with integrated bias fixes for optimal predictions.

## 🎯 **Usage**

Models are automatically loaded by the unified prediction pipeline. No manual intervention required.

```bash
# Run predictions with all available models
python run_unified_pipeline.py
```
