#!/usr/bin/env python3
"""
Create Today's Prediction Summary
Generate a human-readable text summary from today's JSON predictions.
"""

import json
import os
from datetime import datetime as dt

def create_summary():
    """Create a text summary from today's predictions."""
    today_str = dt.now().strftime('%Y-%m-%d')
    json_file = f"cache/predictions/predictions_{today_str}.json"
    
    if not os.path.exists(json_file):
        print(f"❌ No predictions found for {today_str}")
        return
    
    # Load predictions
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    # Create text summary
    summary = []
    summary.append("🎯 BETSIGHTLY FOOTBALL PREDICTIONS")
    summary.append("=" * 80)
    summary.append(f"📅 Date: {data['date']}")
    summary.append(f"🕐 Generated: {data['generation_time']}")
    summary.append(f"🎯 Total High-Quality Predictions: {data['total_predictions']}")
    summary.append(f"📈 Minimum Confidence: {data['min_confidence_used']}%")
    summary.append(f"🔧 Bias Fixes Applied: {data['bias_fixes_applied']}")
    summary.append("=" * 80)
    summary.append("")
    
    # Individual predictions
    summary.append("📊 TODAY'S HIGH-QUALITY PREDICTIONS")
    summary.append("-" * 80)
    
    predictions = data.get('predictions', [])
    for i, pred in enumerate(predictions, 1):
        summary.append(f"\n{i}. {pred['home_team']} vs {pred['away_team']}")
        summary.append(f"   🏆 League: {pred['league']}")
        summary.append(f"   🕐 Time: {pred['match_time']}")
        summary.append(f"   🎯 Prediction: {pred['prediction_type']} = {pred['prediction']}")
        summary.append(f"   📈 Confidence: {pred['confidence']:.1f}%")
        summary.append(f"   🛡️  Risk Level: {pred['risk_level']}")
        summary.append(f"   🆔 Fixture ID: {pred['fixture_id']}")
    
    # Accumulator strategies
    summary.append("\n\n" + "=" * 80)
    summary.append("🎰 ACCUMULATOR STRATEGIES")
    summary.append("=" * 80)
    
    accumulators = data.get('diverse_accumulators', {})
    for strategy_name, selections in accumulators.items():
        summary.append(f"\n🎯 {strategy_name.upper()} STRATEGY")
        summary.append("-" * 50)
        summary.append(f"📊 Number of Games: {len(selections)}")
        
        avg_confidence = sum(s['confidence'] for s in selections) / len(selections)
        summary.append(f"📈 Average Confidence: {avg_confidence:.1f}%")
        summary.append(f"💵 Recommended for: {strategy_name} bettors")
        summary.append("")
        
        summary.append("🎮 GAMES IN THIS ACCUMULATOR:")
        for i, sel in enumerate(selections, 1):
            summary.append(f"{i}. {sel['home_team']} vs {sel['away_team']}")
            summary.append(f"   🎯 Prediction: {sel['prediction_type']} = {sel['prediction']}")
            summary.append(f"   📈 Confidence: {sel['confidence']:.1f}%")
            summary.append(f"   🏆 League: {sel['league']}")
            summary.append(f"   🕐 Time: {sel['match_time']}")
            summary.append("")
    
    # Save to file
    output_file = f"predictions_output/predictions_{today_str}.txt"
    os.makedirs("predictions_output", exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary))
    
    # Also save as latest
    latest_file = "predictions_output/latest_predictions.txt"
    with open(latest_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary))
    
    print(f"✅ Summary created: {output_file}")
    print(f"✅ Latest summary: {latest_file}")
    
    # Print summary to console
    print("\n" + "\n".join(summary))

if __name__ == "__main__":
    create_summary()
