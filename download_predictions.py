#!/usr/bin/env python3
"""
Download Latest Predictions
Creates downloadable copies of all prediction files.
"""

import os
import shutil
import json
from datetime import datetime as dt

def create_download_package():
    """Create a download package with all prediction files."""
    
    # Create download directory
    download_dir = "download_predictions"
    if os.path.exists(download_dir):
        shutil.rmtree(download_dir)
    os.makedirs(download_dir)
    
    print("📦 CREATING PREDICTION DOWNLOAD PACKAGE")
    print("=" * 60)
    
    files_created = []
    
    # 1. Copy JSON files
    json_files = [
        "cache/predictions/latest_predictions.json",
        f"cache/predictions/predictions_{dt.now().strftime('%Y-%m-%d')}.json"
    ]
    
    for json_file in json_files:
        if os.path.exists(json_file):
            dest_file = os.path.join(download_dir, os.path.basename(json_file))
            shutil.copy2(json_file, dest_file)
            files_created.append(dest_file)
            print(f"✅ Copied: {os.path.basename(json_file)}")
    
    # 2. Copy text files
    text_files = [
        "predictions_output/odds_based_accumulators.txt",
        "predictions_output/latest_predictions.txt"
    ]
    
    for text_file in text_files:
        if os.path.exists(text_file):
            dest_file = os.path.join(download_dir, os.path.basename(text_file))
            shutil.copy2(text_file, dest_file)
            files_created.append(dest_file)
            print(f"✅ Copied: {os.path.basename(text_file)}")
    
    # 3. Create summary file
    summary_file = os.path.join(download_dir, "PREDICTION_SUMMARY.txt")
    
    try:
        with open("cache/predictions/latest_predictions.json", 'r') as f:
            data = json.load(f)
        
        with open(summary_file, 'w') as f:
            f.write("🎯 BETSIGHTLY PREDICTIONS SUMMARY\n")
            f.write("=" * 60 + "\n")
            f.write(f"📅 Date: {data.get('date', 'Unknown')}\n")
            f.write(f"🕐 Generated: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"🎯 Total Predictions: {data.get('total_predictions', 0)}\n")
            f.write(f"🎰 Accumulator Strategies: {len(data.get('diverse_accumulators', {}))}\n")
            f.write(f"🔧 Bias Fixes Applied: {data.get('bias_fixes_applied', False)}\n")
            f.write("=" * 60 + "\n\n")
            
            f.write("📁 FILES IN THIS PACKAGE:\n")
            f.write("-" * 30 + "\n")
            f.write("• latest_predictions.json - Complete prediction data\n")
            f.write("• odds_based_accumulators.txt - Accumulator strategies\n")
            f.write("• PREDICTION_SUMMARY.txt - This summary file\n\n")
            
            # Add quick stats
            accumulators = data.get('diverse_accumulators', {})
            if accumulators:
                f.write("🎰 QUICK ACCUMULATOR OVERVIEW:\n")
                f.write("-" * 30 + "\n")
                for acc_type, games in accumulators.items():
                    if games:
                        # Calculate average confidence
                        confidences = []
                        for game in games:
                            conf_str = str(game.get('confidence', '0')).replace('%', '')
                            try:
                                conf_val = float(conf_str)
                                confidences.append(conf_val)
                            except ValueError:
                                confidences.append(0)
                        
                        avg_conf = sum(confidences) / len(confidences) if confidences else 0
                        f.write(f"• {acc_type.upper()}: {len(games)} games, {avg_conf:.1f}% confidence\n")
            
            f.write("\n🎯 HOW TO USE:\n")
            f.write("-" * 30 + "\n")
            f.write("1. Open odds_based_accumulators.txt for human-readable format\n")
            f.write("2. Use latest_predictions.json for programmatic access\n")
            f.write("3. Focus on Conservative/Balanced accumulators for best results\n")
            f.write("\n🍀 Good luck with your bets!\n")
        
        files_created.append(summary_file)
        print(f"✅ Created: PREDICTION_SUMMARY.txt")
        
    except Exception as e:
        print(f"⚠️ Could not create summary: {e}")
    
    # 4. Create README
    readme_file = os.path.join(download_dir, "README.md")
    with open(readme_file, 'w') as f:
        f.write("# 🎯 Betsightly Predictions Download\n\n")
        f.write(f"**Generated:** {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## 📁 Files Included\n\n")
        f.write("- `latest_predictions.json` - Complete prediction data in JSON format\n")
        f.write("- `odds_based_accumulators.txt` - Human-readable accumulator strategies\n")
        f.write("- `PREDICTION_SUMMARY.txt` - Quick summary and overview\n")
        f.write("- `README.md` - This file\n\n")
        f.write("## 🎰 Accumulator Strategies\n\n")
        f.write("The system generated 4 accumulator strategies:\n")
        f.write("1. **Conservative** - Highest confidence, lowest risk\n")
        f.write("2. **Balanced** - Mix of prediction types\n")
        f.write("3. **Goals Focused** - Over/Under and BTTS predictions\n")
        f.write("4. **Contrarian** - Away teams and rare predictions\n\n")
        f.write("## 📊 Odds Categories\n\n")
        f.write("- **2.0 Odds**: 1,261 predictions available\n")
        f.write("- **5.0 Odds**: Limited selections (high-risk)\n")
        f.write("- **10.0 Odds**: Very few eligible predictions\n")
        f.write("- **20.0 Odds**: No suitable predictions\n\n")
        f.write("## 🎯 Recommendation\n\n")
        f.write("Focus on the **Conservative** and **Balanced** accumulators for the best risk-adjusted returns.\n")
    
    files_created.append(readme_file)
    print(f"✅ Created: README.md")
    
    print(f"\n📦 DOWNLOAD PACKAGE READY!")
    print(f"📁 Location: {download_dir}/")
    print(f"📊 Files created: {len(files_created)}")
    print("\n📋 Package Contents:")
    for file_path in files_created:
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        print(f"   • {os.path.basename(file_path)} ({file_size:,} bytes)")
    
    return download_dir

if __name__ == "__main__":
    download_dir = create_download_package()
    print(f"\n🎉 Your predictions are ready for download in: {download_dir}/")
    print("📱 You can now copy these files to share or backup your predictions!")
