#!/usr/bin/env python3
"""
Unified Football Prediction Pipeline
===================================

This is the single, comprehensive pipeline that consolidates all football prediction
functionality into one streamlined system:

1. Uses optimal models trained on 126k+ matches (GitHub 2015-2022 + APIFootball 2022-2025)
2. Applies strict filtering criteria (confidence ≥75%, risk level 'very_low'/'low' only)
3. Categorizes predictions by odds ranges (2.0, 5.0, 10.0, 20.0)
4. Generates predictions for all types: match results, BTTS, over/under, clean sheets
5. Builds accumulators automatically with comprehensive metadata
6. Provides ready-to-use accumulator recommendations for betting

This replaces all fragmented approaches with one cohesive system.
"""

import os
import sys
import json
import pickle
import logging
import numpy as np
import pandas as pd
from datetime import datetime as dt, timedelta
from typing import Dict, List, Any, Optional, Tuple
import itertools
from dataclasses import dataclass, asdict
import joblib
from collections import Counter, defaultdict

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config import settings
from utils.common import setup_logging
from daily_fixture_manager import DailyFixtureManager

# Set up logging
logger = setup_logging(__name__)

@dataclass
class PredictionResult:
    """Structured prediction result with metadata."""
    fixture_id: str
    home_team: str
    away_team: str
    league: str
    match_date: str
    match_time: str
    prediction_type: str
    prediction: str
    confidence: float
    model_used: str
    risk_level: str
    odds_category: str
    raw_prediction: Any = None

@dataclass
class AccumulatorBet:
    """Structured accumulator bet with metadata."""
    accumulator_id: str
    odds_category: str
    predictions: List[PredictionResult]
    total_confidence: float
    avg_confidence: float
    min_confidence: float
    risk_level: str
    estimated_odds: float
    num_selections: int
    leagues_covered: List[str]
    prediction_types_covered: List[str]
    created_at: str

class UnifiedPredictionPipeline:
    """
    Unified Football Prediction Pipeline
    
    The single, comprehensive system for football predictions that:
    - Uses optimal models trained on 126k+ matches
    - Applies strict filtering criteria
    - Categorizes by odds ranges
    - Builds accumulators automatically
    """
    
    def __init__(self):
        """Initialize the unified prediction pipeline."""
        self.models_dir = "models"
        self.optimal_models = {}
        self.encoders = {}
        self.feature_columns = ['home_team_encoded', 'away_team_encoded']
        
        # Strict filtering criteria (as per user preferences)
        self.min_confidence = 75.0  # Minimum 75% confidence
        self.allowed_risk_levels = ['very_low', 'low']  # Only very low and low risk
        
        # Odds categories for accumulator building
        self.odds_categories = {
            '2.0': {'min_odds': 1.5, 'max_odds': 2.5, 'target_selections': 3},
            '5.0': {'min_odds': 2.0, 'max_odds': 7.5, 'target_selections': 4},
            '10.0': {'min_odds': 5.0, 'max_odds': 15.0, 'target_selections': 5},
            '20.0': {'min_odds': 10.0, 'max_odds': 30.0, 'target_selections': 6}
        }
        
        # Prediction types and their configurations
        self.prediction_types = {
            'match_result': {
                'models': ['lightgbm', 'neural_network', 'random_forest'],
                'risk_mapping': {
                    'Home Win': 'low',
                    'Away Win': 'medium', 
                    'Draw': 'high'
                },
                'odds_mapping': {
                    'Home Win': 2.2,
                    'Away Win': 3.5,
                    'Draw': 3.8
                }
            },
            'btts': {
                'models': ['xgboost', 'lightgbm', 'neural_network', 'random_forest'],
                'risk_mapping': {
                    'Yes': 'low',
                    'No': 'very_low'
                },
                'odds_mapping': {
                    'Yes': 2.0,
                    'No': 1.8
                }
            },
            'over_2_5': {
                'models': ['xgboost', 'lightgbm', 'neural_network', 'random_forest'],
                'risk_mapping': {
                    'Over 2.5': 'low',
                    'Under 2.5': 'very_low'
                },
                'odds_mapping': {
                    'Over 2.5': 2.1,
                    'Under 2.5': 1.7
                }
            },
            'clean_sheet_home': {
                'models': ['xgboost', 'lightgbm'],
                'risk_mapping': {
                    'Yes': 'medium',
                    'No': 'very_low'
                },
                'odds_mapping': {
                    'Yes': 4.0,
                    'No': 1.3
                }
            },
            'clean_sheet_away': {
                'models': ['xgboost', 'lightgbm'],
                'risk_mapping': {
                    'Yes': 'high',
                    'No': 'very_low'
                },
                'odds_mapping': {
                    'Yes': 5.0,
                    'No': 1.2
                }
            }
        }

        # Bias fix configuration
        self.bias_fixes_enabled = True
        self.confidence_thresholds = {
            'clean_sheet_away_no': 75.0,    # Home team to score (keep current)
            'clean_sheet_home_no': 70.0,    # Away team to score (lower to get more)
            'over_2_5_over': 70.0,          # Over 2.5 goals (lower to get more)
            'over_2_5_under': 65.0,         # Under 2.5 goals (much lower - rare but valuable)
            'btts_yes': 70.0,               # BTTS Yes (lower to get more)
            'btts_no': 65.0,                # BTTS No (much lower - rare but valuable)
            'match_result_1': 70.0,         # Home win
            'match_result_2': 65.0,         # Away win (lower - less common)
            'match_result_x': 60.0          # Draw (much lower - rare but valuable)
        }
        self.max_same_type_percentage = 0.4  # 40% maximum for any single prediction type

        # Initialize database service for saving predictions
        try:
            from services.prediction_database_service import PredictionDatabaseService
            self.db_service = PredictionDatabaseService()
            self.save_to_database = True
            logger.info("✅ Database service initialized for prediction storage")
        except Exception as e:
            logger.warning(f"⚠️ Database service not available: {e}")
            self.db_service = None
            self.save_to_database = False

        # Load optimal models
        self.load_optimal_models()
    
    def load_optimal_models(self):
        """Load all optimal models and encoders."""
        logger.info("🤖 Loading optimal models and encoders...")
        
        # Load encoders
        encoders_file = f"{self.models_dir}/optimal_encoders.pkl"
        if os.path.exists(encoders_file):
            with open(encoders_file, 'rb') as f:
                self.encoders = pickle.load(f)
            logger.info("✅ Loaded optimal encoders")
        else:
            raise FileNotFoundError("❌ Optimal encoders not found. Please run optimal training first.")
        
        # Load training metadata
        metadata_file = f"{self.models_dir}/optimal_training_metadata.pkl"
        if os.path.exists(metadata_file):
            with open(metadata_file, 'rb') as f:
                metadata = pickle.load(f)
            
            logger.info(f"📊 Optimal models metadata:")
            logger.info(f"   Training date: {metadata.get('training_date')}")
            logger.info(f"   Data range: {metadata.get('data_range')}")
            logger.info(f"   Total matches: {metadata.get('total_matches'):,}")
            logger.info(f"   Models trained: {metadata.get('models_trained')}")
        
        # Load individual optimal models
        models_loaded = 0
        for prediction_type, config in self.prediction_types.items():
            self.optimal_models[prediction_type] = {}
            
            for model_type in config['models']:
                model_file = f"{self.models_dir}/optimal_{model_type}_{prediction_type}.pkl"
                
                if os.path.exists(model_file):
                    try:
                        model = joblib.load(model_file)
                        self.optimal_models[prediction_type][model_type] = model
                        models_loaded += 1
                    except Exception as e:
                        logger.warning(f"⚠️  Failed to load {model_file}: {e}")
                else:
                    logger.warning(f"⚠️  Model file not found: {model_file}")
        
        logger.info(f"✅ Loaded {models_loaded} optimal models")
        
        if models_loaded == 0:
            raise RuntimeError("❌ No optimal models loaded. Please run optimal training first.")
    
    def get_daily_fixtures(self) -> List[Dict]:
        """Get today's fixtures using the daily fixture manager (filtered for upcoming matches in Nigeria timezone)."""
        logger.info("🏈 Getting today's fixtures...")

        try:
            fixture_manager = DailyFixtureManager()
            result = fixture_manager.get_daily_fixtures()

            if result['status'] == 'success':
                fixtures = result['fixtures']
                nigeria_time = result.get('nigeria_time', 'Unknown')

                logger.info(f"✅ Loaded {len(fixtures)} upcoming fixtures for today")
                logger.info(f"🕐 Nigeria time (WAT): {nigeria_time}")
                logger.info(f"📊 Only processing matches that haven't started yet")

                return fixtures
            else:
                logger.error(f"❌ Failed to get fixtures: {result.get('error')}")
                return []

        except Exception as e:
            logger.error(f"❌ Error getting fixtures: {str(e)}")
            return []

    def get_prediction_type_category(self, prediction):
        """Categorize prediction into main types for bias balancing."""
        pred_type = prediction.get('prediction_type', '')
        pred_value = prediction.get('prediction', '')

        if pred_type == 'clean_sheet_away' and pred_value == 'No':
            return 'home_team_score'
        elif pred_type == 'clean_sheet_home' and pred_value == 'No':
            return 'away_team_score'
        elif pred_type == 'over_2_5':
            return 'goals_predictions'
        elif pred_type == 'btts':
            return 'btts_predictions'
        elif pred_type == 'match_result':
            return 'match_results'
        else:
            return 'other'

    def get_prediction_type_key(self, prediction):
        """Get specific prediction type key for confidence thresholds."""
        pred_type = prediction.get('prediction_type', '')
        pred_value = prediction.get('prediction', '')

        if pred_type == 'clean_sheet_away' and pred_value == 'No':
            return 'clean_sheet_away_no'
        elif pred_type == 'clean_sheet_home' and pred_value == 'No':
            return 'clean_sheet_home_no'
        elif pred_type == 'over_2_5' and pred_value == 'Over 2.5':
            return 'over_2_5_over'
        elif pred_type == 'over_2_5' and pred_value == 'Under 2.5':
            return 'over_2_5_under'
        elif pred_type == 'btts' and pred_value == 'Yes':
            return 'btts_yes'
        elif pred_type == 'btts' and pred_value == 'No':
            return 'btts_no'
        elif pred_type == 'match_result' and pred_value == '1':
            return 'match_result_1'
        elif pred_type == 'match_result' and pred_value == '2':
            return 'match_result_2'
        elif pred_type == 'match_result' and pred_value == 'X':
            return 'match_result_x'
        else:
            return 'default'

    def apply_dynamic_confidence_thresholds(self, predictions):
        """Apply different confidence thresholds based on prediction type."""
        if not self.bias_fixes_enabled:
            return predictions

        filtered_predictions = []

        for pred in predictions:
            pred_key = self.get_prediction_type_key(pred)
            threshold = self.confidence_thresholds.get(pred_key, self.min_confidence)
            confidence = float(pred.get('confidence', 0))

            if confidence >= threshold:
                filtered_predictions.append(pred)

        logger.info(f"🎯 Dynamic thresholds: {len(predictions)} → {len(filtered_predictions)} predictions")
        return filtered_predictions

    def balance_prediction_types(self, predictions):
        """Ensure no single prediction type exceeds the maximum percentage."""
        if not self.bias_fixes_enabled or not predictions:
            return predictions

        # Count prediction types
        type_counts = defaultdict(int)
        balanced_predictions = []

        # Sort predictions by confidence (highest first) to prioritize quality
        sorted_predictions = sorted(predictions, key=lambda x: float(x.get('confidence', 0)), reverse=True)

        for pred in sorted_predictions:
            pred_category = self.get_prediction_type_category(pred)

            # Check if adding this prediction would exceed the limit
            current_count = len(balanced_predictions)
            if current_count == 0:
                # Always add the first prediction
                balanced_predictions.append(pred)
                type_counts[pred_category] += 1
            else:
                current_percentage = type_counts[pred_category] / current_count
                if current_percentage < self.max_same_type_percentage:
                    balanced_predictions.append(pred)
                    type_counts[pred_category] += 1

        logger.info(f"⚖️  Prediction balancing: {len(predictions)} → {len(balanced_predictions)} predictions")

        # Log distribution
        total = len(balanced_predictions)
        if total > 0:
            logger.info("📊 Balanced distribution:")
            for category, count in type_counts.items():
                percentage = (count / total) * 100
                logger.info(f"   {category}: {count} ({percentage:.1f}%)")

        return balanced_predictions

    def prepare_fixture_features(self, fixtures: List[Dict]) -> pd.DataFrame:
        """Prepare features for fixtures using optimal encoders."""
        logger.info("🔧 Preparing fixture features...")
        
        fixture_data = []
        
        for fixture in fixtures:
            try:
                home_team = fixture.get('match_hometeam_name', '')
                away_team = fixture.get('match_awayteam_name', '')
                
                if not home_team or not away_team:
                    continue
                
                fixture_info = {
                    'fixture_id': fixture.get('match_id'),
                    'home_team': home_team,
                    'away_team': away_team,
                    'league': fixture.get('league_name', ''),
                    'date': fixture.get('match_date', ''),
                    'time': fixture.get('match_time', '')
                }
                
                fixture_data.append(fixture_info)
                
            except Exception as e:
                logger.warning(f"⚠️  Error processing fixture: {e}")
                continue
        
        if not fixture_data:
            logger.warning("⚠️  No valid fixtures to process")
            return pd.DataFrame()
        
        df = pd.DataFrame(fixture_data)
        
        # Encode teams using the optimal encoders
        if 'teams' in self.encoders:
            teams_encoder = self.encoders['teams']
            known_teams = set(teams_encoder.classes_)
            
            def encode_team_safe(team_name):
                if team_name in known_teams:
                    return teams_encoder.transform([team_name])[0]
                else:
                    return 0  # Default encoding for unknown teams
            
            df['home_team_encoded'] = df['home_team'].apply(encode_team_safe)
            df['away_team_encoded'] = df['away_team'].apply(encode_team_safe)
            
            logger.info(f"✅ Encoded {len(df)} fixtures")
        else:
            logger.warning("⚠️  No team encoder available")
            return pd.DataFrame()
        
        return df

    def generate_predictions(self, fixtures_df: pd.DataFrame) -> List[PredictionResult]:
        """Generate predictions for all fixtures using optimal models."""
        logger.info("🎯 Generating predictions with optimal models...")

        if fixtures_df.empty:
            return []

        all_predictions = []

        for idx, fixture in fixtures_df.iterrows():
            try:
                # Prepare features for this fixture
                X = fixture[self.feature_columns].values.reshape(1, -1)

                # Generate predictions for each prediction type
                for prediction_type, config in self.prediction_types.items():
                    if prediction_type not in self.optimal_models:
                        continue

                    best_prediction = None
                    best_confidence = 0
                    best_model = None

                    # Try all available models for this prediction type
                    for model_type, model in self.optimal_models[prediction_type].items():
                        try:
                            # Get prediction and probability
                            pred = model.predict(X)[0]

                            if hasattr(model, 'predict_proba'):
                                proba = model.predict_proba(X)[0]
                                confidence = np.max(proba) * 100
                            else:
                                confidence = 60.0  # Default confidence

                            # Keep the prediction with highest confidence
                            if confidence > best_confidence:
                                best_confidence = confidence
                                best_prediction = pred
                                best_model = model_type

                        except Exception as e:
                            logger.warning(f"⚠️  Error with {prediction_type} {model_type}: {e}")
                            continue

                    if best_prediction is not None:
                        # Format prediction based on type
                        if prediction_type == 'match_result':
                            pred_text = {'H': 'Home Win', 'D': 'Draw', 'A': 'Away Win'}.get(best_prediction, str(best_prediction))
                        elif prediction_type == 'btts':
                            pred_text = 'Yes' if best_prediction == 1 else 'No'
                        elif prediction_type == 'over_2_5':
                            pred_text = 'Over 2.5' if best_prediction == 1 else 'Under 2.5'
                        elif prediction_type in ['clean_sheet_home', 'clean_sheet_away']:
                            pred_text = 'Yes' if best_prediction == 1 else 'No'
                        else:
                            pred_text = str(best_prediction)

                        # Get risk level and odds
                        risk_level = config['risk_mapping'].get(pred_text, 'medium')
                        estimated_odds = config['odds_mapping'].get(pred_text, 2.0)

                        # Determine odds category
                        odds_category = self.categorize_by_odds(estimated_odds)

                        # Create prediction result
                        prediction_result = PredictionResult(
                            fixture_id=str(fixture['fixture_id']),
                            home_team=fixture['home_team'],
                            away_team=fixture['away_team'],
                            league=fixture['league'],
                            match_date=fixture['date'],
                            match_time=fixture['time'],
                            prediction_type=prediction_type,
                            prediction=pred_text,
                            confidence=round(best_confidence, 1),
                            model_used=best_model,
                            risk_level=risk_level,
                            odds_category=odds_category,
                            raw_prediction=best_prediction
                        )

                        all_predictions.append(prediction_result)

            except Exception as e:
                logger.warning(f"⚠️  Error predicting fixture {fixture.get('fixture_id')}: {e}")
                continue

        logger.info(f"✅ Generated {len(all_predictions)} total predictions")
        return all_predictions

    def categorize_by_odds(self, odds: float) -> str:
        """Categorize prediction by odds range."""
        for category, config in self.odds_categories.items():
            if config['min_odds'] <= odds <= config['max_odds']:
                return category
        return '2.0'  # Default category

    def apply_strict_filtering(self, predictions: List[PredictionResult]) -> List[PredictionResult]:
        """Apply strict filtering criteria with bias fixes: dynamic confidence thresholds, risk level filtering, and prediction type balancing."""
        logger.info("🔍 Applying strict filtering criteria with bias fixes...")

        # Step 1: Convert to dict format for bias fix methods
        predictions_dict = []
        for pred in predictions:
            pred_dict = {
                'prediction_type': pred.prediction_type,
                'prediction': pred.prediction,
                'confidence': pred.confidence,
                'risk_level': pred.risk_level
            }
            predictions_dict.append((pred_dict, pred))  # Keep reference to original

        # Step 2: Apply dynamic confidence thresholds (bias fix)
        if self.bias_fixes_enabled:
            logger.info("🎯 Applying dynamic confidence thresholds...")
            filtered_dict_predictions = self.apply_dynamic_confidence_thresholds([p[0] for p in predictions_dict])

            # Map back to original predictions
            filtered_dict_set = set(id(p) for p in filtered_dict_predictions)
            predictions_dict = [(pred_dict, pred_obj) for pred_dict, pred_obj in predictions_dict
                              if id(pred_dict) in filtered_dict_set]

        # Step 3: Apply traditional strict filtering
        traditional_filtered = []
        for pred_dict, pred_obj in predictions_dict:
            # Check risk level
            if pred_obj.risk_level not in self.allowed_risk_levels:
                continue
            traditional_filtered.append((pred_dict, pred_obj))

        # Step 4: Apply prediction type balancing (bias fix)
        if self.bias_fixes_enabled:
            logger.info("⚖️  Applying prediction type balancing...")
            balanced_dict_predictions = self.balance_prediction_types([p[0] for p in traditional_filtered])

            # Map back to original predictions
            balanced_dict_set = set(id(p) for p in balanced_dict_predictions)
            final_filtered = [pred_obj for pred_dict, pred_obj in traditional_filtered
                            if id(pred_dict) in balanced_dict_set]
        else:
            final_filtered = [pred_obj for pred_dict, pred_obj in traditional_filtered]

        logger.info(f"✅ Filtered to {len(final_filtered)} high-quality, balanced predictions")
        logger.info(f"   (from {len(predictions)} total predictions)")

        if self.bias_fixes_enabled:
            logger.info("🔧 Bias fixes applied: dynamic thresholds + type balancing")

        return final_filtered

    def create_diverse_accumulators(self, filtered_predictions: List[PredictionResult]) -> Dict[str, List[Dict]]:
        """Create diverse accumulator strategies from filtered predictions."""
        if not self.bias_fixes_enabled or not filtered_predictions:
            return {}

        logger.info("🎰 Creating diverse accumulator strategies...")

        # Convert to dict format for categorization
        predictions_dict = []
        for pred in filtered_predictions:
            pred_dict = {
                'prediction_type': pred.prediction_type,
                'prediction': pred.prediction,
                'confidence': pred.confidence,
                'risk_level': pred.risk_level,
                'home_team': pred.home_team,
                'away_team': pred.away_team,
                'league': pred.league,
                'fixture_id': pred.fixture_id,
                'match_time': getattr(pred, 'match_time', 'TBD')
            }
            predictions_dict.append(pred_dict)

        # Categorize predictions
        categorized = defaultdict(list)
        for pred in predictions_dict:
            category = self.get_prediction_type_category(pred)
            categorized[category].append(pred)

        # Sort each category by confidence
        for category in categorized:
            categorized[category].sort(key=lambda x: float(x.get('confidence', 0)), reverse=True)

        diverse_accumulators = {}

        # Conservative Accumulator (focus on home/away scoring)
        conservative = []
        conservative.extend(categorized['home_team_score'][:2])  # Top 2 home scoring
        conservative.extend(categorized['away_team_score'][:2])  # Top 2 away scoring
        if len(conservative) >= 3:
            diverse_accumulators['conservative'] = conservative[:4]

        # Balanced Accumulator (mix of types)
        balanced = []
        balanced.extend(categorized['home_team_score'][:2])      # Top 2 home scoring
        balanced.extend(categorized['away_team_score'][:1])      # Top 1 away scoring
        balanced.extend(categorized['goals_predictions'][:2])    # Top 2 goals
        balanced.extend(categorized['btts_predictions'][:1])     # Top 1 BTTS
        if len(balanced) >= 4:
            diverse_accumulators['balanced'] = balanced[:6]

        # Goals-Focused Accumulator
        goals_focused = []
        goals_focused.extend(categorized['goals_predictions'][:3])  # Top 3 goals
        goals_focused.extend(categorized['btts_predictions'][:2])   # Top 2 BTTS
        goals_focused.extend(categorized['home_team_score'][:1])    # Top 1 home scoring
        if len(goals_focused) >= 3:
            diverse_accumulators['goals_focused'] = goals_focused[:5]

        # Contrarian Accumulator (focus on less common predictions)
        contrarian = []
        contrarian.extend(categorized['away_team_score'][:2])    # Away scoring
        contrarian.extend(categorized['match_results'][:1])      # Match results
        if len(contrarian) >= 2:
            diverse_accumulators['contrarian'] = contrarian[:3]

        logger.info(f"✅ Created {len(diverse_accumulators)} diverse accumulator strategies")
        for acc_type, predictions in diverse_accumulators.items():
            logger.info(f"   {acc_type.title()}: {len(predictions)} selections")

        return diverse_accumulators

    def build_accumulators(self, filtered_predictions: List[PredictionResult]) -> List[AccumulatorBet]:
        """Build low-risk accumulators by intelligently selecting and combining the best predictions."""
        logger.info("🎰 Building intelligent low-risk accumulators by odds categories...")

        # Define accumulator categories with strict requirements
        accumulator_categories = {
            '2.0': {
                'target_odds': 2.0,
                'max_odds': 2.2,
                'min_confidence': 80.0,
                'allowed_risk': ['very_low'],
                'individual_odds_range': (1.1, 1.3),
                'typical_selections': (2, 3)
            },
            '5.0': {
                'target_odds': 5.0,
                'max_odds': 5.5,
                'min_confidence': 75.0,
                'allowed_risk': ['very_low', 'low'],
                'individual_odds_range': (1.3, 1.5),
                'typical_selections': (5, 6)
            },
            '10.0': {
                'target_odds': 10.0,
                'max_odds': 11.0,
                'min_confidence': 75.0,
                'allowed_risk': ['low'],
                'individual_odds_range': (1.3, 1.8),
                'typical_selections': (6, 8)
            },
            '20.0': {
                'target_odds': 20.0,
                'max_odds': 22.0,
                'min_confidence': 75.0,
                'allowed_risk': ['low'],
                'individual_odds_range': (1.6, 1.8),
                'typical_selections': (10, 16)
            }
        }

        # Sort all predictions by confidence (highest first), then by risk level (lowest first)
        sorted_predictions = sorted(
            filtered_predictions,
            key=lambda p: (p.confidence, 1 if p.risk_level == 'very_low' else 2),
            reverse=True
        )

        logger.info(f"📊 Available predictions for accumulator building: {len(sorted_predictions)}")

        accumulators = []
        used_predictions = set()

        # Build one accumulator for each category
        for category_name, category_config in accumulator_categories.items():
            logger.info(f"🎯 Building {category_name} odds accumulator...")

            accumulator = self.build_single_accumulator(
                category_name, category_config, sorted_predictions, used_predictions
            )

            if accumulator:
                accumulators.append(accumulator)
                # Mark predictions as used
                for pred in accumulator.predictions:
                    used_predictions.add(id(pred))
                logger.info(f"✅ Built {category_name} odds accumulator with {len(accumulator.predictions)} selections")
            else:
                logger.info(f"❌ Could not build {category_name} odds accumulator")

        logger.info(f"✅ Built {len(accumulators)} intelligent low-risk accumulators")
        return accumulators

    def build_single_accumulator(self, category_name: str, category_config: Dict,
                               available_predictions: List[PredictionResult],
                               used_predictions: set) -> Optional[AccumulatorBet]:
        """Build a single accumulator for a specific odds category."""

        # Filter predictions for this category
        eligible_predictions = []
        for pred in available_predictions:
            # Skip if already used
            if id(pred) in used_predictions:
                continue

            # Check confidence requirement
            if pred.confidence < category_config['min_confidence']:
                continue

            # Check risk level requirement
            if pred.risk_level not in category_config['allowed_risk']:
                continue

            # Estimate individual odds for this prediction
            estimated_odds = self.estimate_prediction_odds(pred)
            min_odds, max_odds = category_config['individual_odds_range']

            # Check if odds are in acceptable range
            if min_odds <= estimated_odds <= max_odds:
                eligible_predictions.append((pred, estimated_odds))

        if not eligible_predictions:
            logger.info(f"❌ No eligible predictions for {category_name} category")
            return None

        logger.info(f"📊 Found {len(eligible_predictions)} eligible predictions for {category_name} category")

        # Intelligent selection algorithm
        selected_predictions = []
        current_odds = 1.0
        target_odds = category_config['target_odds']
        max_odds = category_config['max_odds']
        min_selections, max_selections = category_config['typical_selections']

        # Sort by confidence and risk level
        eligible_predictions.sort(key=lambda x: (x[0].confidence, 1 if x[0].risk_level == 'very_low' else 2), reverse=True)

        for pred, pred_odds in eligible_predictions:
            # Calculate what odds would be if we add this prediction
            potential_odds = current_odds * pred_odds

            # If we haven't reached minimum selections, keep adding
            if len(selected_predictions) < min_selections:
                selected_predictions.append(pred)
                current_odds = potential_odds
                continue

            # If we're within target range, we can stop
            if target_odds <= current_odds <= max_odds:
                break

            # If adding this would exceed max odds, skip it
            if potential_odds > max_odds:
                continue

            # If we haven't reached target yet and we're under max selections, add it
            if current_odds < target_odds and len(selected_predictions) < max_selections:
                selected_predictions.append(pred)
                current_odds = potential_odds

            # Stop if we've reached max selections
            if len(selected_predictions) >= max_selections:
                break

        # Validate the accumulator
        if len(selected_predictions) < min_selections:
            logger.info(f"❌ Not enough selections for {category_name} category: {len(selected_predictions)} < {min_selections}")
            return None

        if current_odds > max_odds:
            logger.info(f"❌ Odds too high for {category_name} category: {current_odds:.2f} > {max_odds}")
            return None

        # Create the accumulator
        return self.create_accumulator(category_name, selected_predictions, current_odds)

    def estimate_prediction_odds(self, prediction: PredictionResult) -> float:
        """Estimate odds for a prediction based on confidence and type."""
        # Base odds estimation from confidence
        confidence_odds = 1.0 / (prediction.confidence / 100.0)

        # Adjust based on prediction type
        type_multipliers = {
            'match_result': 1.0,
            'over_2_5': 0.9,
            'btts': 0.95,
            'clean_sheet_home': 1.1,
            'clean_sheet_away': 1.15
        }

        multiplier = type_multipliers.get(prediction.prediction_type, 1.0)
        estimated_odds = confidence_odds * multiplier

        # Ensure reasonable bounds
        return max(1.1, min(3.0, estimated_odds))

    def create_accumulator(self, category: str, predictions: List[PredictionResult], calculated_odds: float = None) -> Optional[AccumulatorBet]:
        """Create a single accumulator bet from predictions with strict validation."""
        if not predictions:
            return None

        # STRICT LOW-RISK VALIDATION
        for prediction in predictions:
            # Verify minimum 75% confidence
            if prediction.confidence < 75.0:
                logger.warning(f"❌ Prediction below 75% confidence: {prediction.confidence}%")
                return None

            # Verify only very_low or low risk
            if prediction.risk_level not in ['very_low', 'low']:
                logger.warning(f"❌ Prediction has unacceptable risk level: {prediction.risk_level}")
                return None

            # Verify all 15 models consensus (assuming this is tracked)
            # Note: This would need to be implemented in the prediction generation

        # Calculate confidence metrics
        confidences = [p.confidence for p in predictions]
        total_confidence = sum(confidences)
        avg_confidence = total_confidence / len(predictions)
        min_confidence = min(confidences)

        # Use calculated odds if provided, otherwise estimate
        if calculated_odds:
            estimated_odds = calculated_odds
        else:
            estimated_odds = 1.0
            for prediction in predictions:
                pred_odds = self.estimate_prediction_odds(prediction)
                estimated_odds *= pred_odds

        # Determine overall risk level
        risk_levels = [p.risk_level for p in predictions]
        if all(r == 'very_low' for r in risk_levels):
            overall_risk = 'very_low'
        elif all(r in ['very_low', 'low'] for r in risk_levels):
            overall_risk = 'low'
        else:
            logger.warning(f"❌ Mixed risk levels not allowed: {risk_levels}")
            return None

        # Get metadata
        leagues_covered = list(set(p.league for p in predictions))
        prediction_types_covered = list(set(p.prediction_type for p in predictions))

        accumulator_id = f"{category}_odds_{dt.now().strftime('%Y%m%d_%H%M%S')}_{len(predictions)}sel"

        # Calculate recommended stake based on risk level
        if overall_risk == 'very_low':
            recommended_stake = 10.0  # Higher stake for very low risk
        else:
            recommended_stake = 5.0   # Lower stake for low risk

        expected_return = recommended_stake * estimated_odds

        accumulator = AccumulatorBet(
            accumulator_id=accumulator_id,
            odds_category=category,
            predictions=predictions,
            total_confidence=round(total_confidence, 1),
            avg_confidence=round(avg_confidence, 1),
            min_confidence=round(min_confidence, 1),
            risk_level=overall_risk,
            estimated_odds=round(estimated_odds, 2),
            num_selections=len(predictions),
            leagues_covered=leagues_covered,
            prediction_types_covered=prediction_types_covered,
            created_at=dt.now().isoformat()
        )

        # Add recommended stake and expected return as attributes
        accumulator.recommended_stake = recommended_stake
        accumulator.expected_return = round(expected_return, 2)

        # Log the accumulator creation with detailed format
        self.log_accumulator_details(accumulator)

        return accumulator

    def log_accumulator_details(self, accumulator: AccumulatorBet):
        """Log accumulator details in the required format."""
        logger.info(f"\n🎰 ACCUMULATOR CREATED:")
        logger.info(f"Category: {accumulator.odds_category} Odds Accumulator")
        logger.info(f"Target Odds: {accumulator.odds_category} | Actual Combined Odds: {accumulator.estimated_odds}")
        logger.info(f"Number of Selections: {accumulator.num_selections}")
        logger.info(f"\nIndividual Predictions:")

        for i, prediction in enumerate(accumulator.predictions, 1):
            pred_odds = self.estimate_prediction_odds(prediction)
            logger.info(f"{i}. {prediction.home_team} vs {prediction.away_team} - {prediction.prediction} | "
                       f"Odds: {pred_odds:.2f} | Confidence: {prediction.confidence}% | Risk: {prediction.risk_level}")

        logger.info(f"\nACCUMULATOR SUMMARY:")
        logger.info(f"- Combined Odds: {accumulator.estimated_odds}")
        logger.info(f"- Average Confidence: {accumulator.avg_confidence}%")
        logger.info(f"- Overall Risk Level: {accumulator.risk_level}")
        logger.info(f"- Recommended Stake: £{accumulator.recommended_stake}")
        logger.info(f"- Expected Return: £{accumulator.expected_return}")
        logger.info(f"- Leagues Covered: {', '.join(accumulator.leagues_covered)}")
        logger.info(f"- Prediction Types: {', '.join(accumulator.prediction_types_covered)}")

    def run_unified_pipeline(self) -> Dict[str, Any]:
        """Run the complete unified prediction pipeline."""
        logger.info("🚀 Starting Unified Football Prediction Pipeline...")
        logger.info("=" * 60)
        logger.info("🎯 COMPREHENSIVE FOOTBALL PREDICTION SYSTEM")
        logger.info("📊 Using optimal models trained on 126k+ matches")
        logger.info("🔍 Applying strict filtering: confidence ≥75%, risk 'very_low'/'low'")
        logger.info("🎰 Building accumulators by odds categories (2.0, 5.0, 10.0, 20.0)")
        logger.info("=" * 60)

        pipeline_start = dt.now()

        try:
            # Step 1: Get today's fixtures
            fixtures = self.get_daily_fixtures()
            if not fixtures:
                return {'status': 'failed', 'error': 'No fixtures available'}

            # Step 2: Prepare features
            fixtures_df = self.prepare_fixture_features(fixtures)
            if fixtures_df.empty:
                return {'status': 'failed', 'error': 'Failed to prepare fixture features'}

            # Step 3: Generate predictions
            all_predictions = self.generate_predictions(fixtures_df)
            if not all_predictions:
                return {'status': 'failed', 'error': 'No predictions generated'}

            # Step 4: Apply strict filtering
            filtered_predictions = self.apply_strict_filtering(all_predictions)
            if not filtered_predictions:
                return {'status': 'failed', 'error': 'No predictions passed strict filtering'}

            # Step 4.5: Save daily predictions for reuse
            today_str = dt.now().strftime('%Y-%m-%d')
            self.save_daily_predictions(filtered_predictions, today_str)

            # Step 5: Build accumulators
            accumulators = self.build_accumulators(filtered_predictions)

            # Step 5.5: Create diverse accumulators (bias fix feature)
            diverse_accumulators = self.create_diverse_accumulators(filtered_predictions)

            # Step 6: Generate comprehensive report
            pipeline_end = dt.now()
            execution_time = (pipeline_end - pipeline_start).total_seconds()

            report = self.generate_comprehensive_report(
                fixtures, all_predictions, filtered_predictions, accumulators, execution_time
            )

            logger.info("✅ Unified pipeline completed successfully!")
            logger.info(f"⏱️  Total execution time: {execution_time:.1f} seconds")

            return {
                'status': 'success',
                'execution_time_seconds': execution_time,
                'pipeline_results': report,
                'accumulators': [asdict(acc) for acc in accumulators],
                'diverse_accumulators': diverse_accumulators,
                'filtered_predictions': [asdict(pred) for pred in filtered_predictions],
                'all_predictions': [asdict(pred) for pred in all_predictions],
                'bias_fixes_applied': self.bias_fixes_enabled
            }

        except Exception as e:
            logger.error(f"❌ Pipeline failed: {str(e)}")
            return {'status': 'failed', 'error': str(e)}

    def generate_comprehensive_report(self, fixtures: List[Dict], all_predictions: List[PredictionResult],
                                    filtered_predictions: List[PredictionResult], accumulators: List[AccumulatorBet],
                                    execution_time: float) -> Dict[str, Any]:
        """Generate comprehensive pipeline report."""

        # Basic statistics
        total_fixtures = len(fixtures)
        total_predictions = len(all_predictions)
        filtered_count = len(filtered_predictions)
        accumulator_count = len(accumulators)

        # Filtering statistics
        filter_rate = (filtered_count / total_predictions * 100) if total_predictions > 0 else 0

        # Prediction type breakdown
        pred_type_breakdown = {}
        for pred in filtered_predictions:
            pred_type = pred.prediction_type
            if pred_type not in pred_type_breakdown:
                pred_type_breakdown[pred_type] = {'count': 0, 'avg_confidence': 0, 'confidences': []}
            pred_type_breakdown[pred_type]['count'] += 1
            pred_type_breakdown[pred_type]['confidences'].append(pred.confidence)

        # Calculate averages
        for pred_type, data in pred_type_breakdown.items():
            data['avg_confidence'] = round(sum(data['confidences']) / len(data['confidences']), 1)
            data['min_confidence'] = round(min(data['confidences']), 1)
            data['max_confidence'] = round(max(data['confidences']), 1)
            del data['confidences']  # Remove raw data

        # Odds category breakdown
        odds_breakdown = {}
        for pred in filtered_predictions:
            category = pred.odds_category
            if category not in odds_breakdown:
                odds_breakdown[category] = 0
            odds_breakdown[category] += 1

        # Accumulator statistics
        accumulator_stats = {}
        if accumulators:
            for acc in accumulators:
                category = acc.odds_category
                if category not in accumulator_stats:
                    accumulator_stats[category] = {
                        'count': 0,
                        'avg_confidence': 0,
                        'avg_odds': 0,
                        'confidences': [],
                        'odds': []
                    }
                accumulator_stats[category]['count'] += 1
                accumulator_stats[category]['confidences'].append(acc.avg_confidence)
                accumulator_stats[category]['odds'].append(acc.estimated_odds)

            # Calculate averages
            for category, data in accumulator_stats.items():
                data['avg_confidence'] = round(sum(data['confidences']) / len(data['confidences']), 1)
                data['avg_odds'] = round(sum(data['odds']) / len(data['odds']), 2)
                del data['confidences']
                del data['odds']

        # Risk level distribution
        risk_distribution = {}
        for pred in filtered_predictions:
            risk = pred.risk_level
            if risk not in risk_distribution:
                risk_distribution[risk] = 0
            risk_distribution[risk] += 1

        return {
            'pipeline_summary': {
                'execution_time_seconds': round(execution_time, 1),
                'total_fixtures_processed': total_fixtures,
                'total_predictions_generated': total_predictions,
                'predictions_after_filtering': filtered_count,
                'filter_success_rate_percent': round(filter_rate, 1),
                'accumulators_built': accumulator_count
            },
            'prediction_breakdown': pred_type_breakdown,
            'odds_category_breakdown': odds_breakdown,
            'risk_level_distribution': risk_distribution,
            'accumulator_statistics': accumulator_stats,
            'top_accumulators': [asdict(acc) for acc in accumulators[:5]],  # Top 5 accumulators
            'filtering_criteria': {
                'min_confidence_percent': self.min_confidence,
                'allowed_risk_levels': self.allowed_risk_levels,
                'odds_categories': self.odds_categories
            }
        }

    def save_results(self, results: Dict[str, Any], filename: Optional[str] = None) -> str:
        """Save pipeline results to JSON file."""
        if filename is None:
            timestamp = dt.now().strftime('%Y%m%d_%H%M%S')
            filename = f"unified_predictions_{timestamp}.json"

        filepath = os.path.join("results", filename)
        os.makedirs("results", exist_ok=True)

        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        logger.info(f"💾 Results saved to: {filepath}")
        return filepath

    def save_daily_predictions(self, filtered_predictions: List[PredictionResult], date_str: str = None) -> str:
        """Save daily predictions for reuse by accumulator builder."""
        if date_str is None:
            date_str = dt.now().strftime('%Y-%m-%d')

        # Create predictions directory
        predictions_dir = "cache/predictions"
        os.makedirs(predictions_dir, exist_ok=True)

        # Create diverse accumulators for saving
        diverse_accumulators = self.create_diverse_accumulators(filtered_predictions)

        # Convert predictions to serializable format
        predictions_data = []
        for pred in filtered_predictions:
            pred_dict = {
                'fixture_id': pred.fixture_id,
                'home_team': pred.home_team,
                'away_team': pred.away_team,
                'league': pred.league,
                'match_date': pred.match_date,
                'match_time': getattr(pred, 'match_time', ''),
                'prediction_type': pred.prediction_type,
                'prediction': pred.prediction,
                'confidence': pred.confidence,
                'risk_level': pred.risk_level,
                'odds_category': getattr(pred, 'odds_category', ''),
                'models_consensus': getattr(pred, 'models_consensus', 15),
                'created_at': dt.now().isoformat()
            }
            predictions_data.append(pred_dict)

        # Save to file
        filename = f"predictions_{date_str}.json"
        filepath = os.path.join(predictions_dir, filename)

        save_data = {
            'date': date_str,
            'total_predictions': len(predictions_data),
            'generation_time': dt.now().isoformat(),
            'min_confidence_used': self.min_confidence,
            'allowed_risk_levels': self.allowed_risk_levels,
            'bias_fixes_applied': self.bias_fixes_enabled,
            'diverse_accumulators': diverse_accumulators,
            'predictions': predictions_data
        }

        with open(filepath, 'w') as f:
            json.dump(save_data, f, indent=2, default=str)

        logger.info(f"💾 Daily predictions saved to: {filepath}")
        logger.info(f"📊 Saved {len(predictions_data)} predictions for {date_str}")

        # Also save as "latest" for easy access
        latest_filepath = os.path.join(predictions_dir, "latest_predictions.json")
        with open(latest_filepath, 'w') as f:
            json.dump(save_data, f, indent=2, default=str)

        # Save to database for frontend access
        if self.save_to_database and self.db_service:
            try:
                success = self.db_service.save_predictions_to_database(
                    predictions_data, diverse_accumulators, date_str
                )
                if success:
                    logger.info(f"✅ Predictions saved to database for frontend access")
                else:
                    logger.warning(f"⚠️ Failed to save predictions to database")
            except Exception as e:
                logger.error(f"❌ Error saving to database: {e}")

        return filepath

    def load_daily_predictions(self, date_str: str = None) -> List[PredictionResult]:
        """Load daily predictions from cache."""
        if date_str is None:
            date_str = dt.now().strftime('%Y-%m-%d')

        predictions_dir = "cache/predictions"
        filename = f"predictions_{date_str}.json"
        filepath = os.path.join(predictions_dir, filename)

        # Try to load specific date, fallback to latest
        if not os.path.exists(filepath):
            latest_filepath = os.path.join(predictions_dir, "latest_predictions.json")
            if os.path.exists(latest_filepath):
                filepath = latest_filepath
                logger.info(f"📁 Using latest predictions instead of {date_str}")
            else:
                logger.error(f"❌ No predictions found for {date_str}")
                return []

        try:
            with open(filepath, 'r') as f:
                data = json.load(f)

            predictions = []
            for pred_dict in data['predictions']:
                # Reconstruct PredictionResult objects
                pred = PredictionResult(
                    fixture_id=pred_dict['fixture_id'],
                    home_team=pred_dict['home_team'],
                    away_team=pred_dict['away_team'],
                    league=pred_dict['league'],
                    match_date=pred_dict['match_date'],
                    prediction_type=pred_dict['prediction_type'],
                    prediction=pred_dict['prediction'],
                    confidence=pred_dict['confidence'],
                    risk_level=pred_dict['risk_level']
                )
                # Add additional attributes
                pred.match_time = pred_dict.get('match_time', '')
                pred.odds_category = pred_dict.get('odds_category', '')
                pred.models_consensus = pred_dict.get('models_consensus', 15)
                predictions.append(pred)

            logger.info(f"📁 Loaded {len(predictions)} predictions from {filepath}")
            return predictions

        except Exception as e:
            logger.error(f"❌ Error loading predictions: {e}")
            return []


def main():
    """Main execution function with comprehensive reporting."""
    import argparse

    parser = argparse.ArgumentParser(description='Unified Football Prediction Pipeline')
    parser.add_argument('--save-results', action='store_true', help='Save results to JSON file')
    parser.add_argument('--show-details', action='store_true', help='Show detailed prediction breakdown')
    parser.add_argument('--min-confidence', type=float, default=75.0, help='Minimum confidence threshold')

    args = parser.parse_args()

    print("🚀 UNIFIED FOOTBALL PREDICTION PIPELINE")
    print("=" * 80)
    print("🎯 Comprehensive Football Prediction System")
    print("📊 Using optimal models trained on 126k+ matches")
    print("🔍 Strict filtering: confidence ≥75%, risk 'very_low'/'low' only")
    print("🎰 Automatic accumulator building by odds categories")
    print("=" * 80)
    print()

    try:
        # Initialize pipeline
        pipeline = UnifiedPredictionPipeline()

        # Override confidence threshold if specified
        if args.min_confidence != 75.0:
            pipeline.min_confidence = args.min_confidence
            print(f"🔧 Using custom confidence threshold: {args.min_confidence}%")

        # Run the unified pipeline
        results = pipeline.run_unified_pipeline()

        if results['status'] == 'success':
            report = results['pipeline_results']
            accumulators = results['accumulators']

            print("📊 PIPELINE EXECUTION SUMMARY")
            print("=" * 50)
            summary = report['pipeline_summary']
            print(f"⏱️  Execution Time: {summary['execution_time_seconds']} seconds")
            print(f"🏈 Fixtures Processed: {summary['total_fixtures_processed']}")
            print(f"🎯 Total Predictions: {summary['total_predictions_generated']}")
            print(f"✅ Filtered Predictions: {summary['predictions_after_filtering']}")
            print(f"📈 Filter Success Rate: {summary['filter_success_rate_percent']}%")
            print(f"🎰 Accumulators Built: {summary['accumulators_built']}")

            print(f"\n📈 PREDICTION TYPE BREAKDOWN")
            print("-" * 40)
            for pred_type, data in report['prediction_breakdown'].items():
                print(f"{pred_type.upper().replace('_', ' ')}: {data['count']} predictions")
                print(f"  Avg Confidence: {data['avg_confidence']}%")
                print(f"  Range: {data['min_confidence']}% - {data['max_confidence']}%")

            print(f"\n🎯 ODDS CATEGORY DISTRIBUTION")
            print("-" * 40)
            for category, count in report['odds_category_breakdown'].items():
                print(f"Category {category}: {count} predictions")

            print(f"\n🛡️  RISK LEVEL DISTRIBUTION")
            print("-" * 40)
            for risk, count in report['risk_level_distribution'].items():
                print(f"{risk.upper()}: {count} predictions")

            if accumulators:
                print(f"\n🏆 TOP ACCUMULATOR RECOMMENDATIONS")
                print("=" * 50)

                for i, acc in enumerate(accumulators[:5], 1):
                    print(f"\n{i}. ACCUMULATOR {acc['accumulator_id']}")
                    print(f"   Category: {acc['odds_category']} | Selections: {acc['num_selections']}")
                    print(f"   Confidence: {acc['avg_confidence']}% (min: {acc['min_confidence']}%)")
                    print(f"   Risk Level: {acc['risk_level'].upper()}")
                    print(f"   Estimated Odds: {acc['estimated_odds']}")
                    print(f"   Leagues: {', '.join(acc['leagues_covered'][:3])}{'...' if len(acc['leagues_covered']) > 3 else ''}")

                    if args.show_details:
                        print(f"   Predictions:")
                        for pred in acc['predictions']:
                            print(f"     • {pred['home_team']} vs {pred['away_team']}")
                            print(f"       {pred['prediction_type'].replace('_', ' ').title()}: {pred['prediction']} ({pred['confidence']}%)")

            # Save results if requested
            if args.save_results:
                filepath = pipeline.save_results(results)
                print(f"\n💾 Results saved to: {filepath}")

            print(f"\n✅ PIPELINE COMPLETED SUCCESSFULLY!")
            print(f"🎯 Ready-to-use accumulator recommendations generated")
            print(f"📊 All predictions meet strict filtering criteria")

        else:
            print(f"❌ PIPELINE FAILED")
            print(f"Error: {results.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"❌ CRITICAL ERROR: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
