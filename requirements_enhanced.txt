# 🎯 Betsightly Enhanced Prediction System - Dependencies
# Core ML and Data Processing
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
xgboost>=1.5.0
lightgbm>=3.3.0
scipy>=1.7.0

# Database and ORM
sqlalchemy>=1.4.0
alembic>=1.7.0
psycopg2-binary>=2.9.0  # PostgreSQL support
sqlite3  # Built-in SQLite support

# Web Framework and API
fastapi>=0.68.0
uvicorn[standard]>=0.15.0
pydantic>=1.8.0
python-multipart>=0.0.5
starlette>=0.14.0

# HTTP Requests and API Integration
requests>=2.26.0
httpx>=0.24.0
aiohttp>=3.8.0

# Data Validation and Serialization
marshmallow>=3.13.0
jsonschema>=3.2.0

# Scheduling and Task Management
schedule>=1.1.0
celery>=5.2.0  # Optional for advanced task queuing
redis>=4.0.0   # Optional for Celery backend

# Telegram Integration
python-telegram-bot>=20.0
telegram>=0.0.1

# Environment and Configuration
python-dotenv>=0.19.0
pyyaml>=6.0
configparser>=5.2.0

# Logging and Monitoring
loguru>=0.6.0
structlog>=21.1.0

# Date and Time Handling
python-dateutil>=2.8.0
pytz>=2021.3

# Mathematical and Statistical Libraries
statsmodels>=0.13.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.3.0

# Performance and Optimization
numexpr>=2.7.0
bottleneck>=1.3.0
cython>=0.29.0

# Testing and Development
pytest>=6.2.0
pytest-asyncio>=0.15.0
pytest-cov>=2.12.0
black>=21.9.0
flake8>=3.9.0
mypy>=0.910
isort>=5.9.0

# Security
cryptography>=3.4.0
passlib[bcrypt]>=1.7.0
python-jose[cryptography]>=3.3.0

# File Handling and Utilities
openpyxl>=3.0.0
xlsxwriter>=3.0.0
python-magic>=0.4.0

# Async Support
asyncio>=3.4.0
aiofiles>=0.7.0

# Memory and Performance Monitoring
psutil>=5.8.0
memory-profiler>=0.60.0

# Optional: Advanced ML Libraries
# tensorflow>=2.6.0  # Uncomment if using neural networks
# torch>=1.9.0       # Uncomment if using PyTorch
# keras>=2.6.0       # Uncomment if using Keras

# Optional: Time Series Analysis
# prophet>=1.0.0     # Uncomment for time series forecasting
# statsforecast>=1.0.0  # Uncomment for statistical forecasting

# Optional: Distributed Computing
# dask>=2021.9.0     # Uncomment for distributed computing
# ray>=1.6.0         # Uncomment for distributed ML

# Development and Debugging
ipython>=7.27.0
jupyter>=1.0.0
notebook>=6.4.0

# API Documentation
sphinx>=4.2.0
sphinx-rtd-theme>=1.0.0

# Version Pinning for Stability
# Pin specific versions for production deployment
# fastapi==0.68.1
# uvicorn==0.15.0
# sqlalchemy==1.4.23
# pandas==1.3.3
# numpy==1.21.2
# scikit-learn==1.0.2
# xgboost==1.5.1
# lightgbm==3.3.2
