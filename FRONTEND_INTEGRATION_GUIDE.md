# 🎯 **FRONTEND INTEGRATION GUIDE**
## Complete Database & API Integration for Betsightly Predictions

---

## 📋 **OVERVIEW**

The unified prediction pipeline now **automatically saves predictions to the database** and provides **REST API endpoints** for frontend access. No additional scripts needed!

### ✅ **What's Included**
- **Automatic database storage** of predictions and accumulators
- **REST API endpoints** for frontend consumption
- **Real-time data access** with proper timestamps
- **Comprehensive metadata** for each prediction
- **Multiple accumulator strategies** automatically generated

---

## 🚀 **QUICK START**

### **1. Generate Today's Predictions**
```bash
# Run the unified pipeline (saves to database automatically)
python run_unified_pipeline.py
```

### **2. Start the API Server**
```bash
# Start the API server for frontend access
python api/predictions_api.py
```

### **3. Access Predictions via API**
```bash
# Get today's predictions
GET http://localhost:8000/predictions/today

# Get today's accumulators
GET http://localhost:8000/accumulators/today
```

---

## 🗄️ **DATABASE STORAGE**

### **Tables Used**
1. **`cached_predictions`** - Individual predictions with metadata
2. **`cached_fixtures`** - Match fixture information
3. **`accumulator_cache`** - Accumulator strategies
4. **`enhanced_prediction_batches`** - Daily batch tracking

### **Automatic Storage Process**
When you run `python run_unified_pipeline.py`:

1. ✅ **Predictions generated** with bias fixes applied
2. ✅ **Saved to JSON files** (cache/predictions/)
3. ✅ **Automatically saved to database** for API access
4. ✅ **Accumulator strategies created** and stored
5. ✅ **Batch metadata recorded** with timestamps

---

## 🌐 **API ENDPOINTS**

### **Base URL**: `http://localhost:8000`

### **📊 Core Endpoints**

#### **1. Today's Predictions**
```http
GET /predictions/today
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "date": "2025-08-09",
    "total_predictions": 295,
    "predictions": [
      {
        "id": 1,
        "fixture_id": "614714",
        "home_team": "Team A",
        "away_team": "Team B",
        "league": "Premier League",
        "match_date": "2025-08-09",
        "match_time": "15:00",
        "prediction_type": "match_result",
        "prediction": "1",
        "confidence": 85.5,
        "model_type": "ensemble",
        "created_at": "2025-08-09T14:16:02"
      }
    ],
    "accumulators": {
      "conservative": {
        "id": 1,
        "type": "conservative",
        "total_odds": 1.46,
        "game_count": 4,
        "average_confidence": 93.3,
        "risk_level": "very_low",
        "games": [...],
        "selection_reason": "Auto-generated conservative accumulator"
      }
    },
    "batch_info": {
      "total_fixtures": 203,
      "successful_predictions": 295,
      "status": "completed"
    }
  }
}
```

#### **2. Specific Date Predictions**
```http
GET /predictions/date/2025-08-09
```

#### **3. Date Range Predictions**
```http
GET /predictions/range?start_date=2025-08-01&end_date=2025-08-09
```

#### **4. Today's Accumulators Only**
```http
GET /accumulators/today
```

#### **5. Health Check**
```http
GET /health
```

#### **6. Statistics**
```http
GET /predictions/stats
```

### **🔧 API Features**
- **CORS enabled** for frontend access
- **Automatic error handling** with proper HTTP status codes
- **Comprehensive data validation**
- **Real-time database queries**
- **Interactive API documentation** at `/docs`

---

## 📱 **FRONTEND IMPLEMENTATION**

### **JavaScript/React Example**
```javascript
// Fetch today's predictions
async function getTodaysPredictions() {
  try {
    const response = await fetch('http://localhost:8000/predictions/today');
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('Total predictions:', data.data.total_predictions);
      console.log('Predictions:', data.data.predictions);
      console.log('Accumulators:', data.data.accumulators);
      return data.data;
    }
  } catch (error) {
    console.error('Error fetching predictions:', error);
  }
}

// Fetch specific accumulator strategies
async function getAccumulators() {
  try {
    const response = await fetch('http://localhost:8000/accumulators/today');
    const data = await response.json();
    
    return data.data.accumulators;
  } catch (error) {
    console.error('Error fetching accumulators:', error);
  }
}
```

### **Python Example**
```python
import requests

# Get today's predictions
response = requests.get('http://localhost:8000/predictions/today')
data = response.json()

predictions = data['data']['predictions']
accumulators = data['data']['accumulators']

print(f"Found {len(predictions)} predictions")
print(f"Found {len(accumulators)} accumulator strategies")
```

---

## 🎯 **PREDICTION DATA STRUCTURE**

### **Individual Prediction**
```json
{
  "id": 1,
  "fixture_id": "614714",
  "home_team": "Manchester United",
  "away_team": "Liverpool",
  "league": "Premier League",
  "match_date": "2025-08-09",
  "match_time": "15:00",
  "prediction_type": "match_result",
  "prediction": "1",
  "confidence": 85.5,
  "model_type": "ensemble",
  "created_at": "2025-08-09T14:16:02"
}
```

### **Accumulator Strategy**
```json
{
  "id": 1,
  "type": "conservative",
  "total_odds": 1.46,
  "game_count": 4,
  "average_confidence": 93.3,
  "diversity_score": 75.0,
  "risk_level": "very_low",
  "recommended_stake": 10.0,
  "games": [
    {
      "fixture_id": "614714",
      "home_team": "Team A",
      "away_team": "Team B",
      "prediction": "1",
      "confidence": 95.0,
      "odds": 1.15
    }
  ],
  "selection_reason": "Auto-generated conservative accumulator with 4 selections",
  "created_at": "2025-08-09T14:16:02"
}
```

---

## 🔄 **DAILY WORKFLOW**

### **For Production Use**
1. **Morning**: Run `python run_unified_pipeline.py` to generate today's predictions
2. **API Access**: Frontend fetches predictions via API endpoints
3. **Real-time**: API serves cached predictions instantly
4. **Updates**: Re-run pipeline if needed (overwrites existing data)

### **Automation Options**
```bash
# Cron job example (run daily at 8 AM)
0 8 * * * cd /path/to/betsightly-backend && python run_unified_pipeline.py

# Keep API server running
python api/predictions_api.py &
```

---

## 🛠️ **TROUBLESHOOTING**

### **Common Issues**

#### **1. No Predictions Returned**
```bash
# Check if pipeline has been run today
python test_database_integration.py

# Re-run pipeline
python run_unified_pipeline.py
```

#### **2. API Connection Error**
```bash
# Check if API server is running
curl http://localhost:8000/health

# Start API server
python api/predictions_api.py
```

#### **3. Database Issues**
```bash
# Initialize database
python initialize_enhanced_database.py

# Test database connection
python test_database_integration.py
```

---

## 📊 **PRODUCTION DEPLOYMENT**

### **Environment Variables**
```bash
# Set database URL for production
export DATABASE_URL="postgresql://user:pass@host:port/dbname"

# API configuration
export API_HOST="0.0.0.0"
export API_PORT="8000"
```

### **Docker Deployment**
```dockerfile
# Example Dockerfile
FROM python:3.11
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
EXPOSE 8000
CMD ["python", "api/predictions_api.py"]
```

---

## ✅ **SUMMARY**

### **✅ What Works Now**
1. **Unified pipeline** automatically saves to database
2. **REST API** provides instant access to predictions
3. **Multiple accumulator strategies** generated automatically
4. **Real-time data** with proper timestamps and metadata
5. **Frontend-ready** JSON responses with CORS support

### **🎯 Next Steps for Frontend**
1. **Integrate API calls** into your frontend application
2. **Display predictions** with confidence levels and metadata
3. **Show accumulator strategies** with odds and risk levels
4. **Implement real-time updates** by polling API endpoints
5. **Add error handling** for API failures

### **🚀 Ready for Production**
The system is now **production-ready** with:
- ✅ Database persistence
- ✅ API endpoints
- ✅ Error handling
- ✅ Comprehensive documentation
- ✅ Frontend integration examples

**Your unified prediction pipeline is now fully integrated with database storage and ready for frontend consumption!** 🎉
