#!/usr/bin/env python3
"""
Predictions API
Simple API endpoints for frontend to access predictions from the database.
"""

import sys
import os
from datetime import datetime as dt, timedelta
from typing import Dict, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.prediction_database_service import PredictionDatabaseService

# Initialize FastAPI app
app = FastAPI(
    title="Betsightly Predictions API",
    description="API for accessing football predictions and accumulators",
    version="1.0.0"
)

# Add CORS middleware for frontend access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database service
db_service = PredictionDatabaseService()

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Betsightly Predictions API",
        "version": "1.0.0",
        "endpoints": {
            "today": "/predictions/today",
            "date": "/predictions/date/{date}",
            "range": "/predictions/range",
            "accumulators": "/accumulators/today",
            "health": "/health"
        },
        "timestamp": dt.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Test database connection
        result = db_service.get_todays_predictions()
        db_status = "connected" if result['status'] == 'success' else "error"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    return {
        "status": "healthy",
        "database": db_status,
        "timestamp": dt.now().isoformat()
    }

@app.get("/predictions/today")
async def get_todays_predictions():
    """Get today's predictions and accumulators."""
    try:
        result = db_service.get_todays_predictions()
        
        if result['status'] == 'error':
            raise HTTPException(status_code=500, detail=result['error'])
        
        return {
            "status": "success",
            "data": result,
            "message": f"Retrieved {result['total_predictions']} predictions for today"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/predictions/date/{date}")
async def get_predictions_by_date(date: str):
    """Get predictions for a specific date (YYYY-MM-DD format)."""
    try:
        # Validate date format
        dt.strptime(date, '%Y-%m-%d')
        
        result = db_service.get_todays_predictions(date)
        
        if result['status'] == 'error':
            raise HTTPException(status_code=500, detail=result['error'])
        
        return {
            "status": "success",
            "data": result,
            "message": f"Retrieved {result['total_predictions']} predictions for {date}"
        }
    
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/predictions/range")
async def get_predictions_by_range(
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="End date in YYYY-MM-DD format")
):
    """Get predictions for a date range."""
    try:
        # Validate date formats
        dt.strptime(start_date, '%Y-%m-%d')
        dt.strptime(end_date, '%Y-%m-%d')
        
        result = db_service.get_predictions_by_date_range(start_date, end_date)
        
        if result['status'] == 'error':
            raise HTTPException(status_code=500, detail=result['error'])
        
        return {
            "status": "success",
            "data": result,
            "message": f"Retrieved {result['total_predictions']} predictions from {start_date} to {end_date}"
        }
    
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/accumulators/today")
async def get_todays_accumulators():
    """Get today's accumulator strategies."""
    try:
        result = db_service.get_todays_predictions()
        
        if result['status'] == 'error':
            raise HTTPException(status_code=500, detail=result['error'])
        
        accumulators = result.get('accumulators', {})
        
        return {
            "status": "success",
            "data": {
                "date": result['date'],
                "accumulators": accumulators,
                "total_strategies": len(accumulators)
            },
            "message": f"Retrieved {len(accumulators)} accumulator strategies for today"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/accumulators/date/{date}")
async def get_accumulators_by_date(date: str):
    """Get accumulator strategies for a specific date."""
    try:
        # Validate date format
        dt.strptime(date, '%Y-%m-%d')
        
        result = db_service.get_todays_predictions(date)
        
        if result['status'] == 'error':
            raise HTTPException(status_code=500, detail=result['error'])
        
        accumulators = result.get('accumulators', {})
        
        return {
            "status": "success",
            "data": {
                "date": date,
                "accumulators": accumulators,
                "total_strategies": len(accumulators)
            },
            "message": f"Retrieved {len(accumulators)} accumulator strategies for {date}"
        }
    
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/predictions/stats")
async def get_prediction_stats():
    """Get prediction statistics and summary."""
    try:
        # Get today's predictions
        today_result = db_service.get_todays_predictions()
        
        # Get yesterday's predictions for comparison
        yesterday = (dt.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        yesterday_result = db_service.get_todays_predictions(yesterday)
        
        # Calculate stats
        today_count = today_result.get('total_predictions', 0)
        yesterday_count = yesterday_result.get('total_predictions', 0)
        
        today_accumulators = len(today_result.get('accumulators', {}))
        yesterday_accumulators = len(yesterday_result.get('accumulators', {}))
        
        return {
            "status": "success",
            "data": {
                "today": {
                    "predictions": today_count,
                    "accumulators": today_accumulators,
                    "date": dt.now().strftime('%Y-%m-%d')
                },
                "yesterday": {
                    "predictions": yesterday_count,
                    "accumulators": yesterday_accumulators,
                    "date": yesterday
                },
                "comparison": {
                    "predictions_change": today_count - yesterday_count,
                    "accumulators_change": today_accumulators - yesterday_accumulators
                }
            },
            "message": "Prediction statistics retrieved successfully"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Betsightly Predictions API...")
    print("📊 Available endpoints:")
    print("   GET /predictions/today - Today's predictions")
    print("   GET /predictions/date/{date} - Predictions for specific date")
    print("   GET /predictions/range?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD - Date range")
    print("   GET /accumulators/today - Today's accumulators")
    print("   GET /health - Health check")
    print("   GET /predictions/stats - Statistics")
    print("\n🌐 API will be available at: http://localhost:8000")
    print("📖 API documentation at: http://localhost:8000/docs")
    
    uvicorn.run(
        "predictions_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
